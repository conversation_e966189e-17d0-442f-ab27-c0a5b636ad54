"""
Castle token generation for Atlassian Register.

This module provides Castle token generation for fraud detection bypass
during the registration process.
"""

import json
import time
import random
import hashlib
import base64
import math
import logging
from typing import Dict, Any, List
from .config import RegistrationConfig


logger = logging.getLogger(__name__)


class CastleTokenGenerator:
    """Castle token generator for fraud detection."""
    
    def __init__(self, config: RegistrationConfig):
        """Initialize the Castle token generator with configuration."""
        self.config = config
        self.user_agent = config.user_agent
        self.language = config.browser_language
        self.timezone = config.browser_timezone
        # Use email as seed for consistent fingerprints
        self.fingerprint_seed = hash(config.email) % 2147483647
    
    def generate_token(self) -> str:
        """
        Generate a Castle token for fraud detection.
        
        Returns:
            Base64 encoded Castle token
        """
        logger.debug("Generating Castle token")
        
        # Collect browser fingerprint data
        fingerprint_data = self._collect_fingerprint_data()
        
        # Create Castle token payload
        token_payload = self._create_token_payload(fingerprint_data)
        
        # Encode token
        token = self._encode_token(token_payload)
        
        logger.debug(f"Generated Castle token: {token[:50]}...")
        return token

    def _generate_realistic_canvas_fingerprint(self) -> str:
        """Generate realistic Canvas fingerprint based on Picasso algorithm."""
        # Use seed for consistent results
        random.seed(self.fingerprint_seed)

        # Simulate Canvas rendering with realistic parameters
        canvas_data = {
            "width": 300,
            "height": 300,
            "shapes": 5,
            "text_metrics": {
                "font_family": "Arial",
                "font_size": 12,
                "baseline": "alphabetic"
            },
            "rendering_context": {
                "antialias": True,
                "color_depth": 24,
                "pixel_ratio": 1.0
            }
        }

        # Generate hash based on "rendered" content
        content_str = json.dumps(canvas_data, sort_keys=True)
        canvas_hash = hashlib.md5(content_str.encode()).hexdigest()

        # Reset random seed
        random.seed()
        return canvas_hash

    def _generate_realistic_webgl_fingerprint(self) -> Dict[str, Any]:
        """Generate realistic WebGL fingerprint."""
        # Use seed for consistent results
        random.seed(self.fingerprint_seed)

        # Realistic GPU configurations
        gpu_configs = [
            {"vendor": "NVIDIA Corporation", "renderer": "NVIDIA GeForce GTX 1060"},
            {"vendor": "AMD", "renderer": "AMD Radeon RX 580"},
            {"vendor": "Intel Inc.", "renderer": "Intel(R) UHD Graphics 620"},
            {"vendor": "NVIDIA Corporation", "renderer": "NVIDIA GeForce RTX 3070"},
            {"vendor": "AMD", "renderer": "AMD Radeon RX 6700 XT"}
        ]

        gpu = gpu_configs[self.fingerprint_seed % len(gpu_configs)]

        webgl_data = {
            "vendor": gpu["vendor"],
            "renderer": gpu["renderer"],
            "version": "WebGL 1.0 (OpenGL ES 2.0 Chromium)",
            "shading_language_version": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)",
            "extensions": [
                "ANGLE_instanced_arrays",
                "EXT_blend_minmax",
                "EXT_color_buffer_half_float",
                "EXT_disjoint_timer_query",
                "EXT_float_blend",
                "EXT_frag_depth",
                "EXT_shader_texture_lod",
                "EXT_texture_compression_bptc",
                "EXT_texture_compression_rgtc",
                "EXT_texture_filter_anisotropic",
                "WEBKIT_EXT_texture_filter_anisotropic"
            ],
            "max_texture_size": 16384,
            "max_viewport_dims": [16384, 16384],
            "aliased_line_width_range": [1, 1],
            "aliased_point_size_range": [1, 1024]
        }

        # Reset random seed
        random.seed()
        return webgl_data

    def _generate_realistic_performance_data(self) -> Dict[str, Any]:
        """Generate realistic performance timing data."""
        # Use seed for consistent results
        random.seed(self.fingerprint_seed)

        # Base timing (simulating page load)
        base_time = int(time.time() * 1000)

        # Realistic timing ranges based on research
        dns_time = random.randint(1, 50)
        connect_time = random.randint(10, 200)
        request_time = random.randint(50, 500)
        response_time = random.randint(100, 800)
        dom_time = random.randint(100, 2000)
        load_time = random.randint(200, 3000)

        performance_data = {
            "navigationStart": base_time,
            "domainLookupStart": base_time + 1,
            "domainLookupEnd": base_time + 1 + dns_time,
            "connectStart": base_time + 1 + dns_time,
            "connectEnd": base_time + 1 + dns_time + connect_time,
            "requestStart": base_time + 1 + dns_time + connect_time,
            "responseStart": base_time + 1 + dns_time + connect_time + request_time,
            "responseEnd": base_time + 1 + dns_time + connect_time + request_time + response_time,
            "domContentLoadedEventEnd": base_time + 1 + dns_time + connect_time + request_time + response_time + dom_time,
            "loadEventEnd": base_time + 1 + dns_time + connect_time + request_time + response_time + dom_time + load_time
        }

        # Reset random seed
        random.seed()
        return performance_data

    def _generate_realistic_memory_data(self) -> Dict[str, Any]:
        """Generate realistic memory usage data."""
        # Use seed for consistent results
        random.seed(self.fingerprint_seed)

        # Realistic memory ranges (in bytes)
        # Light page: 5-20MB, Medium: 20-100MB, Heavy: 100-500MB
        base_memory = random.randint(15, 80) * 1024 * 1024  # 15-80MB
        used_memory = int(base_memory * random.uniform(0.6, 0.9))  # 60-90% usage
        limit_memory = int(base_memory * random.uniform(1.5, 3.0))  # 1.5-3x total

        memory_data = {
            "usedJSHeapSize": used_memory,
            "totalJSHeapSize": base_memory,
            "jsHeapSizeLimit": limit_memory
        }

        # Reset random seed
        random.seed()
        return memory_data

    def _collect_fingerprint_data(self) -> Dict[str, Any]:
        """Collect browser fingerprint data."""
        return {
            "screen": {
                "width": 1920,
                "height": 1080,
                "colorDepth": 24,
                "pixelDepth": 24,
                "availWidth": 1920,
                "availHeight": 1040
            },
            "navigator": {
                "userAgent": self.user_agent,
                "language": self.language.split(',')[0],
                "languages": self.language.split(','),
                "platform": "Win32",
                "cookieEnabled": True,
                "doNotTrack": None,
                "hardwareConcurrency": 8,
                "maxTouchPoints": 0,
                "vendor": "Google Inc.",
                "vendorSub": "",
                "productSub": "20030107",
                "appName": "Netscape",
                "appVersion": self.user_agent.split('Mozilla/')[1] if 'Mozilla/' in self.user_agent else "5.0",
                "onLine": True
            },
            "window": {
                "innerWidth": 1920,
                "innerHeight": 937,
                "outerWidth": 1920,
                "outerHeight": 1080,
                "devicePixelRatio": 1,
                "screenX": 0,
                "screenY": 0
            },
            "document": {
                "referrer": "",
                "title": "Sign up for Atlassian",
                "url": "https://id.atlassian.com/signup",
                "domain": "id.atlassian.com",
                "characterSet": "UTF-8",
                "readyState": "complete"
            },
            "timezone": {
                "offset": self._get_timezone_offset(),
                "name": self.timezone
            },
            "plugins": self._get_plugin_list(),
            "fonts": self._get_font_list(),
            "canvas": self._generate_realistic_canvas_fingerprint(),
            "webgl": self._generate_realistic_webgl_fingerprint(),
            "audio": self._generate_audio_fingerprint(),
            "performance": {
                "timing": self._generate_realistic_performance_data(),
                "memory": self._generate_realistic_memory_data()
            }
        }
    
    def _get_timezone_offset(self) -> int:
        """Get timezone offset in minutes."""
        timezone_offsets = {
            "America/New_York": 300,  # EST
            "America/Los_Angeles": 480,  # PST
            "Europe/London": 0,  # GMT
            "Asia/Shanghai": -480,  # CST
            "Asia/Tokyo": -540  # JST
        }
        return timezone_offsets.get(self.timezone, 0)
    
    def _get_plugin_list(self) -> List[Dict[str, str]]:
        """Get list of browser plugins."""
        return [
            {"name": "PDF Viewer", "filename": "internal-pdf-viewer"},
            {"name": "Chrome PDF Viewer", "filename": "mhjfbmdgcfjbbpaeojofohoefgiehjai"},
            {"name": "Chromium PDF Viewer", "filename": "mhjfbmdgcfjbbpaeojofohoefgiehjai"},
            {"name": "Microsoft Edge PDF Viewer", "filename": "mhjfbmdgcfjbbpaeojofohoefgiehjai"},
            {"name": "WebKit built-in PDF", "filename": "internal-pdf-viewer"}
        ]
    
    def _get_font_list(self) -> List[str]:
        """Get list of available fonts."""
        return [
            "Arial", "Arial Black", "Arial Narrow", "Calibri", "Cambria",
            "Cambria Math", "Comic Sans MS", "Consolas", "Courier New",
            "Georgia", "Impact", "Lucida Console", "Lucida Sans Unicode",
            "Microsoft Sans Serif", "Palatino Linotype", "Segoe UI",
            "Tahoma", "Times New Roman", "Trebuchet MS", "Verdana"
        ]
    
    def _generate_canvas_fingerprint(self) -> str:
        """Generate canvas fingerprint."""
        # Simulate canvas rendering
        canvas_text = "Castle fingerprint test 🔒"
        return hashlib.md5(canvas_text.encode()).hexdigest()
    
    def _generate_webgl_fingerprint(self) -> Dict[str, Any]:
        """Generate WebGL fingerprint."""
        return {
            "vendor": "Google Inc. (Intel)",
            "renderer": "ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11)",
            "version": "WebGL 1.0 (OpenGL ES 2.0 Chromium)",
            "shadingLanguageVersion": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)",
            "extensions": [
                "ANGLE_instanced_arrays", "EXT_blend_minmax", "EXT_color_buffer_half_float",
                "EXT_disjoint_timer_query", "EXT_float_blend", "EXT_frag_depth",
                "EXT_shader_texture_lod", "EXT_texture_compression_bptc",
                "EXT_texture_compression_rgtc", "EXT_texture_filter_anisotropic",
                "WEBKIT_EXT_texture_filter_anisotropic", "EXT_sRGB", "KHR_parallel_shader_compile",
                "OES_element_index_uint", "OES_fbo_render_mipmap", "OES_standard_derivatives",
                "OES_texture_float", "OES_texture_float_linear", "OES_texture_half_float",
                "OES_texture_half_float_linear", "OES_vertex_array_object", "WEBGL_color_buffer_float",
                "WEBGL_compressed_texture_s3tc", "WEBKIT_WEBGL_compressed_texture_s3tc",
                "WEBGL_compressed_texture_s3tc_srgb", "WEBGL_debug_renderer_info",
                "WEBGL_debug_shaders", "WEBGL_depth_texture", "WEBKIT_WEBGL_depth_texture",
                "WEBGL_draw_buffers", "WEBGL_lose_context", "WEBKIT_WEBGL_lose_context"
            ]
        }
    
    def _generate_audio_fingerprint(self) -> str:
        """Generate audio context fingerprint."""
        # Simulate audio context properties
        audio_data = f"sampleRate:44100,maxChannelCount:2,numberOfInputs:1,numberOfOutputs:1"
        return hashlib.md5(audio_data.encode()).hexdigest()
    
    def _generate_performance_timing(self) -> Dict[str, int]:
        """Generate performance timing data."""
        base_time = int(time.time() * 1000)
        return {
            "navigationStart": base_time - random.randint(1000, 3000),
            "unloadEventStart": 0,
            "unloadEventEnd": 0,
            "redirectStart": 0,
            "redirectEnd": 0,
            "fetchStart": base_time - random.randint(800, 1200),
            "domainLookupStart": base_time - random.randint(700, 1000),
            "domainLookupEnd": base_time - random.randint(600, 900),
            "connectStart": base_time - random.randint(500, 800),
            "connectEnd": base_time - random.randint(400, 700),
            "secureConnectionStart": base_time - random.randint(450, 750),
            "requestStart": base_time - random.randint(300, 600),
            "responseStart": base_time - random.randint(200, 500),
            "responseEnd": base_time - random.randint(100, 400),
            "domLoading": base_time - random.randint(50, 300),
            "domInteractive": base_time - random.randint(20, 200),
            "domContentLoadedEventStart": base_time - random.randint(10, 100),
            "domContentLoadedEventEnd": base_time - random.randint(5, 50),
            "domComplete": base_time - random.randint(1, 20),
            "loadEventStart": base_time - random.randint(0, 10),
            "loadEventEnd": base_time
        }
    
    def _create_token_payload(self, fingerprint_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create Castle token payload."""
        timestamp = int(time.time() * 1000)
        
        return {
            "timestamp": timestamp,
            "fingerprint": fingerprint_data,
            "events": [
                {
                    "type": "page_view",
                    "timestamp": timestamp - random.randint(100, 1000),
                    "url": "https://id.atlassian.com/signup"
                },
                {
                    "type": "form_interaction",
                    "timestamp": timestamp - random.randint(50, 500),
                    "element": "email"
                }
            ],
            "version": "1.0",
            "client_id": self._generate_client_id()
        }
    
    def _generate_client_id(self) -> str:
        """Generate a unique client ID."""
        # Create a deterministic but unique client ID
        data = f"{self.user_agent}{self.timezone}{time.time()}"
        return hashlib.sha256(data.encode()).hexdigest()[:32]
    
    def _encode_token(self, payload: Dict[str, Any]) -> str:
        """Encode the token payload to base64."""
        json_payload = json.dumps(payload, separators=(',', ':'))
        encoded = base64.b64encode(json_payload.encode()).decode()
        
        # Add some random padding to match expected format
        padding_length = random.randint(10, 50)
        padding = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_', k=padding_length))
        
        return f"{encoded}{padding}"
