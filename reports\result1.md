下面按时序把抓包里**真正和业务逻辑相关**的往返（request / response）挑出来，列出重点字段，最后给出可脚本化的思路。
（包号均对应 `atlassian‑register.zip/raw/XXX_[c|s].txt` 中的文件，方便你自己再去核对）

**⚠️ 重要更新**：经过深入包分析，发现实际注册流程与初步分析有重大差异，以下为修正后的准确流程。

---

## 1 注册页面初始化

| 步   | 报文              | 方法 & URL                                  | 关键要点                                                                                                                                   |
| --- | --------------- | ----------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------- |
| 1‑a | 147\_c / 147\_s | **GET `https://id.atlassian.com/signup`** | 200 OK，下载表单 HTML。响应里下发了第一批 Cookie（`atlassian.account.xsrf.token`、`ajs_anonymous_id`、`cloud.session.token` 等）。这些后续每个调用都要随 Cookie 头一起带上。 |

**重要发现**：`atlassian.account.xsrf.token` 在不同端点使用不同的头部名称：
- 邮箱验证时使用 `x-csrf-token` 头部
- 站点操作时使用 `xsrf-token` 头部
- API令牌创建时不需要显式XSRF头部

---

## 2 提交邮箱 + 密码

| 步   | 报文              | 方法 & URL                                                       | Payload 摘要                                                                                                                                                                                            |
| --- | --------------- | -------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 2‑a | 225\_c / 225\_s | **POST `/gateway/api/gasv3/api/v1/batch`** (referer = /signup) | `content‑type: text/plain`，里面是批量 GraphQL/REST 调用数组。核心片段是 `signupWithEmail`，字段：`email":"<EMAIL>","password":"114514#atlassian","displayName":"rovo"` 等。返回 200，表示账号被创建，服务器马上向邮箱发验证码。 |

> ⚠️ 这里会同时带上 Google reCAPTCHA 的 token 字段，实际脚本里要么用 **无头浏览器** 自动做 reCAPTCHA，要么改走 Atlassian 身份接口（见最下方思路）。

---

## 3 验证码验证

| 步   | 报文              | 方法 & URL                                   | Payload 摘要                                                                                                                                   |
| --- | --------------- | ------------------------------------------ | -------------------------------------------------------------------------------------------------------------------------------------------- |
| 3‑a | 254\_c / 254\_s | **POST `/gateway/api/gasv3/api/v1/batch`** | 同样放在 batch 里，动作叫 `verifyEmailWithCode`，字段：`"code":"W9L6WE"`。成功后账号生效，返回里带 `accountId` (`712020:dcaf3d79‑…`) 及新的 `cloud.session.token` Cookie。 |

---

## 4 登录 Session 就绪

* 浏览器随后会自动 GET `/login?state=…` 数个 302，这里省略。
* 只要保证 **Cookie**: `cloud.session.token`、`atlassian.account.xsrf.token`、`atlassian.account.ffs.id` 同步保存，后续即视为已登录状态。

---

## 5 创建 API Token

| 步   | 报文     | 方法 & URL                                                      | Payload / 响应                                                                                               |
| --- | ------ | ------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------- |
| 5‑a | 727\_c | **POST `/gateway/api/users/{accountId}/manage/api-tokens`**   | Header：`x-atlassian-token:` = 前面 xsrf token<br>Body：`{"label":"rovo","expiry":"2026-06-16T00:00:00.000Z"}` |
| 5‑b | 727\_s | 200 OK，JSON 返回<br>`"token":"ATATT3xFfGF0_Ae-4tlj0….3E86D956"` | 这就是新生成的 **API Token**，只出现一次；务必当场保存。                                                                        |

---

## 6 申请 DevAI 云站点

| 步   | 报文                                                                                                                                           | 方法 & URL                                                                          | Payload 摘要                                                                                        |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------- |
| 6‑a | 1048\_c / 1048\_s                                                                                                                            | **POST `https://www.atlassian.com/gateway/api/bxp/signup/site/recommended-name`** | Body：`{"email":"<EMAIL>"}` → 返回 `{"recommendedName":"wendavid-team-y60a2ps6"}` |
| 6‑b | 后续若继续点“Create site”会触发 `/gateway/api/bxp/signup/site/create` 之类调用（本抓包里只到推荐名）。实际上要再发一次 **POST** 把 `siteName`、要安装的产品（devai 套件 ID）一并提交即可完成站点创建。 |                                                                                   |                                                                                                   |

---

## Cookie & 头部一览（在 5‑a 前需同时存在）

```
Cookie: atlassian.account.xsrf.token=<...>; cloud.session.token=<...>; 
        atlassian.account.ffs.id=<...>; ajs_anonymous_id=<...>; atlCohort=<...>; ...
Header: x-atlassian-token: <同 atlassian.account.xsrf.token 的值>
```

如抓包所示，**xsrf token = Cookie 值，去掉 URI‑escape 后直接放进请求头即可**，不需要再算签名。

---

## 如何脚本化

1. **首选方案：用无头浏览器**（Puppeteer / Playwright）。

   * 把真正卡人的两处 —— reCAPTCHA & `site/create` 页面上的前端校验 —— 交给浏览器执行。
   * 关键请求可在 `page.on('request')` 时拦截并提取，再按上表重放。

2. **纯 HTTP 方案**（适用于批量注册 / 无图形环境）：

   1. `GET /signup` → 抓取并保存所有 Set‑Cookie。
   2. 调用 **`/gateway/api/gasv3/api/v1/batch`**

      * 把抓包里 `signupWithEmail` 的 JSON 复制出来，
      * 把其中 `g-recaptcha-response` 字段换成自己的 token（可用 2Captcha、CapSolver 等服务生成）。
   3. 同理再发一次 batch 做 `verifyEmailWithCode`。
   4. 登陆后的 Cookies 中取 `atlassian.account.xsrf.token`。
   5. **创建 API token**：

      ```bash
      curl -X POST "https://id.atlassian.com/gateway/api/users/{accountId}/manage/api-tokens" \
           -H "x-atlassian-token: $XSRF" \
           -H "Cookie: $ALL_COOKIES" \
           -H "Content-Type: application/json" \
           --data '{"label":"rovo","expiry":"2026-06-16T00:00:00.000Z"}'
      ```

      成功 JSON 里字段 `"token"` 即为最终密钥。
   6. **站点创建** 需要额外 API：

      ```bash
      curl -X POST "https://www.atlassian.com/gateway/api/bxp/signup/site/create" \
           -H "xsrf-token: $XSRF" -H "Cookie: $ALL_COOKIES" \
           -d '{"siteName":"wendavid-team-y60a2ps6","bundle":"devai"}'
      ```

      返回里可取 `siteUrl`、`orgId` 等信息。

---

### 友情提醒

* Atlassian 对同 IP / 同邮箱短时多次注册有速率限制，脚本里最好加随机延迟。
* API Token 拿到后即具备 **全部账号权限**，请安全存放；失泄可随时 `DELETE /manage/api-tokens/{id}` 失效。
* 如果后续想完全绕过浏览器，可以考虑直接用 Atlassian 的 **Identity Public API**（OAuth‑2 flow）创建账号并生成 PAT，但目前官方文档仅面向企业级客户，需要另外开通。

有了这些关键信息，你就可以把整个流转进自动化脚本中：前半段用浏览器 or 重放批量 API，后半段用常规 REST 调用即可。祝顺利！
