"""
Command-line interface for Atlassian Register tool.
"""

import argparse
import sys
import logging
from pathlib import Path
from typing import Optional

from .config import RegistrationConfig
from .flow import RegistrationFlow


def setup_logging(verbose: bool = False, debug: bool = False) -> None:
    """Setup logging configuration."""
    if debug:
        level = logging.DEBUG
    elif verbose:
        level = logging.INFO
    else:
        level = logging.WARNING
    
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def create_parser() -> argparse.ArgumentParser:
    """Create and configure argument parser."""
    parser = argparse.ArgumentParser(
        prog='atlassian-register',
        description='Automated Atlassian account registration and site creation tool',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  atlassian-register --email <EMAIL> --password mypass123
  atlassian-register --config config.env --verbose
  atlassian-register --email <EMAIL> --dry-run
        """
    )
    
    # Configuration options
    config_group = parser.add_argument_group('Configuration')
    config_group.add_argument(
        '--config', '-c',
        type=Path,
        help='Path to configuration file (.env format)'
    )
    config_group.add_argument(
        '--email', '-e',
        help='Email address for registration'
    )
    config_group.add_argument(
        '--password', '-p',
        help='Password for registration'
    )
    config_group.add_argument(
        '--site-name', '-s',
        help='Desired site name (optional, will use recommended if not provided)'
    )
    
    # CAPTCHA options
    captcha_group = parser.add_argument_group('CAPTCHA Configuration')
    captcha_group.add_argument(
        '--yescaptcha-key',
        help='YesCaptcha API key'
    )
    captcha_group.add_argument(
        '--captcha-timeout',
        type=int,
        default=120,
        help='CAPTCHA solving timeout in seconds (default: 120)'
    )
    
    # Execution options
    exec_group = parser.add_argument_group('Execution Options')
    exec_group.add_argument(
        '--dry-run',
        action='store_true',
        help='Perform a dry run without actual registration'
    )
    exec_group.add_argument(
        '--manual-verification',
        action='store_true',
        help='Enable manual verification checkpoints'
    )
    exec_group.add_argument(
        '--delay',
        type=float,
        default=2.0,
        help='Delay between requests in seconds (default: 2.0)'
    )
    
    # Output options
    output_group = parser.add_argument_group('Output Options')
    output_group.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose output'
    )
    output_group.add_argument(
        '--debug', '-d',
        action='store_true',
        help='Enable debug output'
    )
    output_group.add_argument(
        '--output', '-o',
        type=Path,
        help='Output file for registration results (JSON format)'
    )
    
    return parser


def main() -> int:
    """Main CLI entry point."""
    parser = create_parser()
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(verbose=args.verbose, debug=args.debug)
    logger = logging.getLogger(__name__)
    
    try:
        # Load configuration
        config = RegistrationConfig.from_args(args)
        
        if args.verbose:
            logger.info("Starting Atlassian registration process...")
            logger.info(f"Email: {config.email}")
            logger.info(f"Site name: {config.site_name or 'Auto-generated'}")
            logger.info(f"Dry run: {args.dry_run}")
        
        # Create and run registration flow
        flow = RegistrationFlow(config)
        
        if args.dry_run:
            logger.info("Dry run mode - no actual registration will be performed")
            result = flow.validate_configuration()
        else:
            result = flow.run_registration(
                manual_verification=args.manual_verification,
                delay=args.delay
            )
        
        # Output results
        if args.output:
            result.save_to_file(args.output)
            logger.info(f"Results saved to {args.output}")
        
        if result.success:
            print("✅ Registration completed successfully!")
            print(f"Account ID: {result.account_id}")
            print(f"API Token: {result.api_token}")
            print(f"Site URL: {result.site_url}")
            return 0
        else:
            print("❌ Registration failed!")
            print(f"Error: {result.error_message}")
            return 1
            
    except KeyboardInterrupt:
        logger.info("Registration cancelled by user")
        return 130
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=args.debug)
        return 1


if __name__ == '__main__':
    sys.exit(main())
