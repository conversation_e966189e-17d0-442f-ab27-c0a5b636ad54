# Design Document / 设计文档

## Overview / 概述

This design document outlines the architecture for automating Atlassian account registration based on comprehensive network packet analysis from the `raw/` directory. The system follows KISS principles and uses a simple, linear workflow that replicates the exact HTTP calls observed in the packet captures, including the actual registration and verification flows discovered through detailed packet analysis.

本设计文档概述了基于`raw/`目录全面网络包分析的Atlassian账户注册自动化架构。系统遵循KISS原则，使用简单的线性工作流程，复制在包捕获中观察到的确切HTTP调用，包括通过详细包分析发现的实际注册和验证流程。

## Architecture / 架构

### High-Level Flow / 高级流程

Based on detailed packet analysis from `result1.md` and actual captured packets, the complete flow is:

基于`result1.md`的详细包分析和实际捕获的包，完整流程为：

```
1. Initialize Session (GET /signup) → Extract Initial Cookies [147_c.txt]
2. Register Account (POST /rest/signup) → Handle reCAPTCHA & Castle Token [243_c.txt]
3. Verify Email (POST /rest/signup/verify-email/otp) → Get AccountID [257_c.txt]
4. Create API Token (POST /manage/api-tokens) → Extract Token [727_c.txt]
5. Get Site Name (POST /site/recommended-name) → Get Recommended Name [1048_c.txt]
6. Create Site (POST /activate-product) → Complete DevAI Setup [1070_c.txt]
```

### Core Components / 核心组件

The system consists of three main modules following KISS principles:

系统由三个遵循KISS原则的主要模块组成：

1. **AtlassianClient** - HTTP client with cookie management / 带cookie管理的HTTP客户端
2. **CaptchaSolver** - YesCaptcha integration / YesCaptcha集成
3. **RegistrationFlow** - Main orchestration logic / 主要编排逻辑

## Components and Interfaces / 组件和接口

### 1. AtlassianClient Class

**Purpose / 目的:** Handle all HTTP communications with Atlassian services, manage cookies and headers based on packet captures.

处理与Atlassian服务的所有HTTP通信，基于包捕获管理cookies和头部。

**Key Methods / 关键方法:**
- `get_signup_page()` - Replicates 147_c.txt request (GET /signup)
- `register_account(email, password, display_name, recaptcha_token, castle_token)` - Replicates 243_c.txt (POST /rest/signup)
- `verify_email(otp_code, token)` - Replicates 257_c.txt (POST /rest/signup/verify-email/otp)
- `create_api_token(account_id, label, expiry)` - Replicates 727_c.txt (POST /manage/api-tokens)
- `get_recommended_site_name(email)` - Replicates 1048_c.txt (POST /site/recommended-name)
- `create_site(site_name, display_name, email, products, recaptcha_token)` - Replicates 1070_c.txt (POST /activate-product)

**Cookie Management / Cookie管理:**
- Automatically extract and store cookies from responses (Set-Cookie headers)
- Include all required cookies in subsequent requests
- Handle dual XSRF token systems:
  - `id.atlassian.com`: Use `x-csrf-token` header (from 257_c.txt)
  - `www.atlassian.com`: Use `xsrf-token` header (from 1048_c.txt, 1070_c.txt)
- Manage session tokens and account ID cookies across domain transitions

### 2. CaptchaSolver Class

**Purpose / 目的:** Interface with YesCaptcha service for automated reCAPTCHA solving. Handles multiple reCAPTCHA instances throughout the flow.

与YesCaptcha服务接口进行自动reCAPTCHA解决。处理整个流程中的多个reCAPTCHA实例。

**Key Methods / 关键方法:**
- `solve_recaptcha(site_key, page_url, action=None)` - Submit CAPTCHA task (supports both invisible and visible)
- `get_solution(task_id)` - Poll for solution
- `wait_for_solution(task_id, timeout=300)` - Wait with polling
- `solve_signup_captcha()` - Specific handler for signup reCAPTCHA (site key: 6Le9VxMnAAAAALKRlj8jmKwUYmFTZWcF0y4o9QeD)
- `solve_site_creation_captcha()` - Specific handler for site creation reCAPTCHA

### 3. RegistrationFlow Class

**Purpose / 目的:** Orchestrate the complete registration process with manual verification steps and proper error handling.

编排完整的注册流程并包含手动验证步骤和适当的错误处理。

**Key Methods / 关键方法:**
- `run_registration(email, password, display_name, verification_code)` - Main orchestration flow
- `verify_step(step_name, expected_data)` - Manual verification helper
- `handle_domain_transition()` - Manage cookie transfer between id.atlassian.com and www.atlassian.com
- `extract_verification_token(response)` - Extract JWT token from registration response for email verification
- `wait_for_email_verification()` - Handle email verification code input with timeout

## Data Models / 数据模型

### RegistrationConfig
```python
@dataclass
class RegistrationConfig:
    email: str
    password: str
    display_name: str
    verification_code: str  # Email OTP code
    yescaptcha_api_key: str
    token_label: str = "automation-token"
    token_expiry: str = "2026-06-16T00:00:00.000Z"
    site_name: Optional[str] = None  # If None, will get recommended name
    timezone: str = "Asia/Shanghai"
    consent_marketing: bool = False  # General marketing opt-in
```

### RegistrationResult
```python
@dataclass
class RegistrationResult:
    account_id: str  # Format: "712020:dcaf3d79-4727-4807-811d-17bacea4a4c4"
    api_token: str   # Format: "ATATT3xFfGF0_Ae-4tlj0..."
    site_url: str    # The created Atlassian site URL
    org_id: str      # Organization ID for the created site
    site_name: str   # The actual site name used
    cookies: Dict[str, str]  # Final session cookies
    verification_token: str  # JWT token from registration for verification
```

### CastleTokenGenerator
```python
class CastleTokenGenerator:
    """Generates Castle fraud detection tokens required for signup"""
    def generate_token(self, user_agent: str, screen_resolution: tuple) -> str:
        # Implementation based on Castle.js fingerprinting
        pass
```

## Error Handling / 错误处理

Following KISS principles, error handling will be simple and direct:

遵循KISS原则，错误处理将简单直接：

1. **HTTP Errors** - Raise exceptions with clear messages
2. **CAPTCHA Failures** - Retry up to 3 times, then fail
3. **Rate Limiting** - Implement simple exponential backoff
4. **Verification Failures** - Stop process and report error

## Testing Strategy / 测试策略

**No Automated Testing** - Following requirements, no unit tests or automated tests will be implemented.

**无自动化测试** - 遵循需求，不会实现单元测试或自动化测试。

**Manual Verification Steps** - After each major step:

**手动验证步骤** - 在每个主要步骤后：

1. Print request/response details for comparison with packet captures
2. Verify cookies are correctly extracted and stored
3. Confirm API responses match expected formats from `raw/` files
4. Manual inspection of generated tokens and site URLs

## Implementation Details / 实现细节

### HTTP Headers Replication / HTTP头部复制

Based on actual packet analysis from raw/ directory, each request must include specific headers. Critical discovery: Different endpoints use different authentication methods.

基于raw/目录实际包分析，每个请求必须包含特定头部。关键发现：不同端点使用不同的认证方法。

```python
# From 147_c.txt - Initial signup page
headers_signup = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
    'Accept-Encoding': 'gzip, deflate, br, zstd',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-site': 'none',
    'sec-fetch-mode': 'navigate',
    'sec-fetch-user': '?1',
    'sec-fetch-dest': 'document',
    'upgrade-insecure-requests': '1'
}

# From 243_c.txt - Actual registration (NOT batch API as previously thought)
headers_registration = {
    'Content-Type': 'application/json',  # Standard JSON for /rest/signup
    'Origin': 'https://id.atlassian.com',
    'Referer': 'https://id.atlassian.com/signup?email=...',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-mode': 'cors',
    'sec-fetch-dest': 'empty'
}

# From 257_c.txt - Email verification (uses x-csrf-token)
headers_email_verify = {
    'Content-Type': 'application/json',
    'x-csrf-token': '<xsrf_token_value>',  # Note: x-csrf-token, not x-atlassian-token
    'Origin': 'https://id.atlassian.com',
    'Referer': 'https://id.atlassian.com/signup/verify-email/otp?token=...',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-mode': 'cors',
    'sec-fetch-dest': 'empty'
}

# From 727_c.txt - API Token creation (no explicit XSRF header in packet)
headers_api_token = {
    'Content-Type': 'application/json',
    'Origin': 'https://id.atlassian.com',
    'Referer': 'https://id.atlassian.com/manage-profile/security/api-tokens',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-mode': 'cors',
    'sec-fetch-dest': 'empty'
}

# From 1048_c.txt - Site name recommendation (www.atlassian.com domain)
headers_site_api = {
    'Content-Type': 'application/json',
    'Origin': 'https://www.atlassian.com',
    'Referer': 'https://www.atlassian.com/try/cloud/signup?bundle=devai',
    'xsrf-token': '<extracted_xsrf_token>',  # Different header name for www domain
    'web-platform': 'bxp-web-platform',
    'x-atl-cohort-id': '94'
}

# From 1070_c.txt - Site creation/product activation
headers_site_creation = {
    'Content-Type': 'application/json',
    'Origin': 'https://www.atlassian.com',
    'Referer': 'https://www.atlassian.com/try/cloud/signup?bundle=devai',
    'xsrf-token': '<extracted_xsrf_token>',
    'web-platform': 'bxp-web-platform',
    'x-atl-cohort-id': '94',
    'recaptcha-site-key': '6Le9VxMnAAAAALKRlj8jmKwUYmFTZWcF0y4o9QeD'
}
```

### Cookie Extraction Pattern / Cookie提取模式

Based on actual packet analysis, the following cookies are critical. **Key Discovery**: Different XSRF token handling for different domains.

基于实际包分析，以下cookies至关重要。**关键发现**：不同域名使用不同的XSRF令牌处理方式。

```python
def extract_cookies(response):
    """Extract cookies following the pattern from packet captures"""
    # From 147_s.txt initial response - cookies are set via Set-Cookie headers
    # From subsequent requests - cookies must be included in requests

    required_cookies = {
        # Core authentication cookies (set during initial signup page load)
        'atlassian.account.xsrf.token': '************************f2e962395baf',  # From 243_c.txt
        'atlassian.account.ffs.id': '************************879d23a4d3c1',
        'ajs_anonymous_id': '%227abed30e-9ce5-4418-ae3c-c95ffc79a15f%22',
        '__cuid': '068989461cdc4c56b92f671b23991b37',

        # Session cookies (after successful registration and verification)
        'cloud.session.token': '**************************************************************************...',  # JWT token
        '__aid_user_id': '712020%3Adcaf3d79-4727-4807-811d-17bacea4a4c4',  # URL encoded account ID
        'atlCohort': '{"bucketAll":{"bucketedAtUTC":"2025-07-27T14:44:10.806Z","version":"2","index":94,"bucketId":0}}',
        'atlUserHash': '**********',

        # Site creation specific (from 1048_c.txt, 1070_c.txt)
        'BXP_SIGNUP_SESSION_ID': 's%3ATOAVIkhANANbkF69kDp9Qvga4uHKtXq4.yNnARvBqlrEOnLd9U4s4p58VT21PXA4eaEtNYaIKwfA',
        'login_user_detected': 'true',
        'wac_user_detected': '1',
        'OptanonConsent': '...',  # Cookie consent data

        # Additional tracking cookies
        'atl_xid.xc': '%7B%22value%22%3A%2279f8fcf5-eb75-468e-83b0-d7ea711ff477%22...',
        'atl_session': '46169d0f-9f19-49d8-be7d-94d13b5aaa9b'
    }

    # XSRF token handling (CRITICAL DISCOVERY):
    # - For id.atlassian.com email verification: use 'x-csrf-token' header (from 257_c.txt)
    # - For id.atlassian.com API token creation: NO explicit XSRF header required (from 727_c.txt)
    # - For www.atlassian.com site operations: use 'xsrf-token' header (from 1048_c.txt, 1070_c.txt)
    return required_cookies
```

### Request Payload Templates / 请求负载模板

Based on actual packet analysis from raw/ directory. **Major Discovery**: The actual registration flow uses REST endpoints, not GraphQL batch operations.

基于raw/目录的实际包分析。**重大发现**：实际注册流程使用REST端点，而非GraphQL批量操作。

```python
# From 243_c.txt - ACTUAL registration payload (REST API, not batch)
registration_payload = {
    "challenge-response": {
        "challenge-kind": "recaptcha",
        "token": {
            "value": "03AFcWeA5Y0FaghRr2Jwrek_ju_1gzlL_EVVPDwgPyqjb5EhlzL3Q7Zdz4Sid0Ew7Zv_Y5N3i7ZQR7iSQJvaBm5L1atVLkxDvxe_jGOY9xzxJX...",
            "type": "score"
        }
    },
    "email": "<EMAIL>",
    "display": None,  # Display name is None in actual packet
    "castleToken": "kp6RHDZQ6HrbpEkpw-AuZEw-4zAZB_GLwNVHn8_510BKkRpk9JTxdEO_0Au4-UOBrYKVMNlNaBGlKEZn_8tkd_4maUN..."
}

# From 257_c.txt - ACTUAL email verification payload (REST API)
email_verify_payload = {
    "otpCode": "W9L6WE",  # The verification code
    "token": "eyJraWQiOiJtaWNyb3MvYWlkLWFjY291bnQvOTRmb3MxN3FlY2wyaHU3NyIsImFsZyI6IlJTMjU2In0..."  # JWT token from registration
}

# From 727_c.txt - API Token creation payload
api_token_payload = {
    "label": "rovo",
    "expiry": "2026-06-16T00:00:00.000Z"
}

# From 1048_c.txt - Site name recommendation payload
site_name_payload = {
    "email": "<EMAIL>"
}

# From 1070_c.txt - ACTUAL site creation payload (DevAI product activation)
site_creation_payload = {
    "displayName": "atla",
    "email": "<EMAIL>",
    "ccpProducts": [{
        "offeringId": "d69d4faa-7677-48f2-a432-fda525d2e223",
        "productKey": "devai.ondemand",
        "pricingType": "FREE",
        "edition": "free",
        "chargeElement": "user"
    }],
    "siteName": "wendavid-team-y60a2ps6",
    "timezone": "Asia/Shanghai",
    "useCcpPlatform": False,
    "consent": {
        "site": "atlassian",
        "locale": "SG",
        "formUrl": "https://www.atlassian.com/try/cloud/signup?bundle=devai",
        "consents": [
            {"key": "termsOfService", "displayedText": "", "granted": True},
            {"key": "privacyPolicy", "displayedText": "...", "granted": True},
            {"key": "generalMarketingOptIn", "displayedText": "", "granted": False}
        ]
    },
    "platform": "routing",
    "signupContext": "{\"signupId\":\"3b08c527-372b-4f83-baad-b4e563d5cadd\",\"bxpGatewayAnchor\":{\"timestamp\":\"2025-07-27T14:45:34.941Z\"}}",
    "isNewUser": True,
    "gRecaptchaResponse": "03AFcWeA6BuWO2e3aNKK5Og5-F4rNfHal6XbIT0uG_CIhfQRSOiwCj4PfZOvhJQT_jcBAE_5bBKkuhWm_FIq5r_aF660psVE8Rha4jSKiIhP13m7wi7VfuXY..."
}

# Analytics payload (from 225_c.txt) - for tracking only, not functional
analytics_payload = {
    "batch": [{
        "context": {
            "locale": "en",
            "screen": {"width": 1920, "height": 1080, "density": 1},
            "library": {"name": "analytics.js", "version": "4.27.1"}
        },
        "type": "track",
        "properties": {
            "actionSubject": "score-recaptcha",
            "action": "loaded-recaptcha"
        }
    }]
}
```

## Security Considerations / 安全考虑

1. **API Token Storage** - Store tokens securely, never log them
2. **Credential Handling** - Use environment variables for sensitive data
3. **Rate Limiting** - Respect Atlassian's service limits
4. **CAPTCHA Service** - Secure YesCaptcha API key handling

## Dependencies / 依赖项

Using `uv` for dependency management as specified in requirements:

使用`uv`进行依赖管理，如需求中所指定：

### Core Dependencies / 核心依赖
- `requests>=2.31.0` - HTTP client library for API calls
- `python-dotenv>=1.0.0` - Environment variable management
- `urllib3>=2.0.0` - Advanced HTTP handling and connection pooling

### Optional Dependencies / 可选依赖
- `pycryptodome>=3.19.0` - For Castle token generation (if implementing locally)
- `fake-useragent>=1.4.0` - For realistic User-Agent rotation
- `python-jose>=3.3.0` - For JWT token handling and validation

### Built-in Modules / 内置模块
- `dataclasses` - Data structure definitions
- `json` - JSON handling
- `time` - Delays and timeouts
- `os` - Environment access
- `urllib.parse` - URL encoding/decoding
- `base64` - Token encoding
- `hashlib` - Fingerprinting support
- `random` - Random delays and jitter

## MCP Integration / MCP集成

The implementation will leverage MCP tools extensively during development:

实现将在开发过程中广泛利用MCP工具：

### Development Phase MCP Usage / 开发阶段MCP使用

1. **HTTP Client Development** - Use context7 to access:
   - `requests` library documentation and best practices
   - Session management patterns
   - Cookie handling examples
   - Error handling strategies

2. **Authentication Implementation** - Reference through MCP:
   - JWT token handling documentation
   - CSRF protection patterns
   - Session management across domains

3. **Anti-Fraud Integration** - Research through MCP:
   - Browser fingerprinting techniques
   - Castle.js implementation patterns
   - reCAPTCHA integration methods

4. **Error Handling Design** - Access documentation for:
   - HTTP status code handling
   - Retry logic implementation
   - Exponential backoff patterns

### Runtime MCP Integration / 运行时MCP集成

1. **YesCaptcha Service Integration** - Use MCP to access:
   - YesCaptcha API documentation
   - Integration examples and best practices
   - Error handling for CAPTCHA services

2. **Debugging and Troubleshooting** - Leverage MCP for:
   - HTTP debugging techniques
   - Network troubleshooting guides
   - Packet analysis tools and methods

## Summary / 总结

This design document provides a comprehensive architecture for automating Atlassian account registration based on detailed packet analysis. The key achievements of this design include:

本设计文档基于详细的包分析，为Atlassian账户注册自动化提供了全面的架构。本设计的主要成就包括：

### Major Discoveries / 主要发现

1. **Complete Flow Mapping** - Successfully identified all critical API endpoints through packet analysis
2. **Authentication Complexity** - Discovered dual XSRF token systems and domain-specific requirements
3. **Anti-Fraud Measures** - Identified Castle token and multiple reCAPTCHA requirements
4. **DevAI Integration** - Mapped complete DevAI site creation flow with specific product configurations

### Implementation Readiness / 实现就绪性

The design provides:
- Exact HTTP request/response formats from packet captures
- Complete cookie management strategy
- Comprehensive error handling approach
- Security and anti-fraud considerations
- Performance and reliability guidelines

### Next Steps / 下一步

This design document serves as the foundation for the implementation tasks outlined in `tasks.md`. The detailed packet analysis and discovered endpoints provide a clear roadmap for building a robust, production-ready Atlassian registration automation system.

本设计文档为`tasks.md`中概述的实现任务奠定了基础。详细的包分析和发现的端点为构建强大的、生产就绪的Atlassian注册自动化系统提供了清晰的路线图。

## Key Findings from Packet Analysis / 包分析关键发现

### Critical Discoveries / 关键发现

1. **Account ID Format**: `712020:dcaf3d79-4727-4807-811d-17bacea4a4c4` (from 254_c.txt, 727_c.txt)
2. **API Token Format**: `ATATT3xFfGF0_Ae-4tlj0kimJiIPsgeO9QAa-88b9Ez5otcFPCugeT56v08iFPKLJt0aDXUxfY6koMRo5zmCfLnJp1kD7Xw10Bpr9rjX0z0rxai6qiKadbqp8FOOMDiVYMJl8q72DU8g0i_C1UL5rbQDbYyBzYo8BkzWtDwJq4PLPhEaneTQDrA=3E86D956` (from 727_s.txt)
3. **Site Name Format**: `wendavid-team-y60a2ps6` (from 1048_s.txt)
4. **Different XSRF Handling**: 
   - `id.atlassian.com` uses `x-atlassian-token` header
   - `www.atlassian.com` uses `xsrf-token` header
5. **Content-Type Variations**:
   - Batch API: `text/plain` (not `application/json`)
   - API Token: `application/json`
   - Site API: `application/json`

### Complete Session Flow Discovery / 完整会话流程发现

**MAJOR UPDATE**: Found the actual registration and verification packets. The flow is now complete:

**重大更新**：找到了实际的注册和验证包。流程现在完整了：

1. **Initial GET** (147): Sets basic cookies (`atlassian.account.xsrf.token`, `ajs_anonymous_id`, etc.)
2. **Analytics Tracking** (225): Tracks reCAPTCHA loading (optional)
3. **Actual Registration** (243): POST to `/rest/signup` with reCAPTCHA and Castle token
4. **Email Verification** (257): POST to `/rest/signup/verify-email/otp` with OTP code and JWT token
5. **API Token Creation** (727): POST to `/manage/api-tokens` with account ID
6. **Site Name Recommendation** (1048): POST to `www.atlassian.com` for site name
7. **Site Creation** (1070): POST to `/activate-product` with DevAI bundle

### Critical Implementation Discoveries / 关键实现发现

1. **Registration is REST, not GraphQL**: Uses `/rest/signup` endpoint, not batch API
2. **Castle Token Required**: Anti-fraud token must be generated for registration
3. **JWT Token Flow**: Registration returns JWT token needed for email verification
4. **Dual XSRF Systems**: Different header names for different domains
5. **Domain Transition**: Must handle cookie transfer from `id.atlassian.com` to `www.atlassian.com`
6. **Multiple reCAPTCHA**: Both registration and site creation require reCAPTCHA solving
7. **DevAI Bundle**: Specific product offering ID required for DevAI site creation

## Verification Process / 验证流程

After each implementation step, verify against actual packet captures:

在每个实现步骤后，根据实际包捕获进行验证：

1. **Request Format Validation** - Match exact request format with captured packets:
   - 147_c.txt (Initial signup page)
   - 243_c.txt (Registration with Castle token)
   - 257_c.txt (Email verification with JWT)
   - 727_c.txt (API token creation)
   - 1048_c.txt (Site name recommendation)
   - 1070_c.txt (Site creation with DevAI bundle)

2. **Cookie Management Verification** - Ensure cookies match patterns from packet headers:
   - Initial cookies from 147_s.txt
   - Session cookies after verification
   - Domain transition cookies for www.atlassian.com

3. **Response Parsing Validation** - Verify response handling matches server responses:
   - Account ID extraction from verification response
   - API token extraction from 727_s.txt format
   - Site name from 1048_s.txt format

4. **Authentication Flow Testing** - Verify XSRF token handling:
   - `x-csrf-token` for id.atlassian.com email verification
   - `xsrf-token` for www.atlassian.com site operations
   - No explicit XSRF for API token creation

5. **End-to-End Integration** - Test complete flow with manual verification checkpoints:
   - Account ID format: `712020:dcaf3d79-4727-4807-811d-17bacea4a4c4`
   - API token format: `ATATT3xFfGF0_Ae-4tlj0...`
   - Site name format: `wendavid-team-y60a2ps6`

6. **Error Handling Validation** - Test failure scenarios and recovery mechanisms

## Critical Implementation Notes / 关键实现注意事项

### Security and Anti-Fraud Measures / 安全和反欺诈措施

1. **Castle Token Generation**: Registration requires a Castle.js fraud detection token. This is a complex fingerprinting system that includes:
   - Browser fingerprinting data
   - Screen resolution and device characteristics
   - Behavioral patterns
   - Network characteristics

2. **reCAPTCHA Integration**: Two separate reCAPTCHA challenges:
   - Registration reCAPTCHA (site key: `6Le9VxMnAAAAALKRlj8jmKwUYmFTZWcF0y4o9QeD`)
   - Site creation reCAPTCHA (same site key, different context)

3. **Rate Limiting Considerations**:
   - Atlassian implements sophisticated rate limiting
   - Same IP/email restrictions for repeated registrations
   - Random delays and jitter recommended

### Domain and Authentication Complexity / 域名和认证复杂性

1. **Multi-Domain Flow**: The process spans two domains:
   - `id.atlassian.com` for account management
   - `www.atlassian.com` for site creation

2. **XSRF Token Variations**:
   - Email verification: `x-csrf-token` header
   - API token creation: No explicit XSRF header required
   - Site operations: `xsrf-token` header

3. **Cookie Synchronization**: Must maintain session state across domain transitions

### DevAI-Specific Requirements / DevAI特定需求

1. **Product Bundle Configuration**: DevAI requires specific offering ID:
   - `offeringId`: `d69d4faa-7677-48f2-a432-fda525d2e223`
   - `productKey`: `devai.ondemand`
   - `edition`: `free`

2. **Consent Management**: Specific consent structure required for DevAI signup

3. **Timezone Handling**: Must specify timezone for site creation (default: `Asia/Shanghai`)

### Error Scenarios and Handling / 错误场景和处理

1. **Registration Failures**:
   - Invalid reCAPTCHA response
   - Castle token validation failure
   - Email already registered
   - Rate limiting triggered

2. **Verification Failures**:
   - Invalid OTP code
   - Expired JWT token
   - Session timeout

3. **Site Creation Failures**:
   - Site name already taken
   - Product offering unavailable
   - Insufficient permissions

### Performance and Reliability / 性能和可靠性

1. **Request Timing**: Implement appropriate delays between requests to mimic human behavior
2. **Retry Logic**: Exponential backoff for transient failures
3. **Session Management**: Proper cleanup and token refresh handling
4. **Logging**: Comprehensive logging for debugging without exposing sensitive data