# Atlassian Register - 使用指南

## 快速开始

### 1. 环境准备

确保您的系统已安装：
- Python 3.13+
- uv 包管理器

### 2. 安装

```bash
# 克隆项目
git clone <repository-url>
cd atlassian_register

# 创建虚拟环境并安装依赖
uv venv
uv add requests python-dotenv urllib3

# 安装项目
uv pip install -e .
```

### 3. 配置

复制环境变量模板：
```bash
cp .env.template .env
```

编辑 `.env` 文件，填入必要的配置：
```env
# 必需配置
ATLASSIAN_EMAIL=<EMAIL>
ATLASSIAN_PASSWORD=your-secure-password
YESCAPTCHA_API_KEY=your-yescaptcha-api-key

# 可选配置
ATLASSIAN_SITE_NAME=my-awesome-site
CAPTCHA_TIMEOUT=120
REQUEST_DELAY=2.0
```

### 4. 基本使用

```bash
# 使用配置文件运行
uv run atlassian-register --config .env

# 直接指定参数
uv run atlassian-register --email <EMAIL> --password mypass123 --yescaptcha-key your-key

# 干运行模式（仅验证配置）
uv run atlassian-register --config .env --dry-run

# 启用详细输出
uv run atlassian-register --config .env --verbose

# 启用手动验证检查点
uv run atlassian-register --config .env --manual-verification --debug
```

## 详细配置

### 环境变量说明

| 变量名 | 必需 | 默认值 | 说明 |
|--------|------|--------|------|
| `ATLASSIAN_EMAIL` | ✅ | - | 注册邮箱地址 |
| `ATLASSIAN_PASSWORD` | ✅ | - | 账户密码（至少8位） |
| `YESCAPTCHA_API_KEY` | ✅ | - | YesCaptcha API密钥 |
| `ATLASSIAN_SITE_NAME` | ❌ | 自动生成 | 站点名称 |
| `CAPTCHA_TIMEOUT` | ❌ | 120 | CAPTCHA求解超时（秒） |
| `REQUEST_DELAY` | ❌ | 2.0 | 请求间延迟（秒） |
| `MAX_RETRIES` | ❌ | 3 | 最大重试次数 |
| `RETRY_DELAY` | ❌ | 5.0 | 重试延迟（秒） |
| `MANUAL_VERIFICATION` | ❌ | false | 启用手动验证 |
| `DEBUG_MODE` | ❌ | false | 调试模式 |
| `VERBOSE_LOGGING` | ❌ | false | 详细日志 |

### 高级配置

```env
# 浏览器指纹配置
USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
BROWSER_LANGUAGE=en-US,en;q=0.9
BROWSER_TIMEZONE=America/New_York

# 代理配置
HTTP_PROXY=http://proxy.example.com:8080
HTTPS_PROXY=https://proxy.example.com:8080

# 持久化配置
ENABLE_COOKIES_PERSISTENCE=true
COOKIES_FILE=.cookies.json
ENABLE_REQUEST_LOGGING=false
REQUEST_LOG_FILE=requests.log
```

## 使用示例

### 示例 1: 基本注册

```bash
uv run atlassian-register \
  --email <EMAIL> \
  --password MySecurePass123 \
  --yescaptcha-key abc123def456 \
  --verbose
```

### 示例 2: 使用配置文件

创建 `my-config.env`:
```env
ATLASSIAN_EMAIL=<EMAIL>
ATLASSIAN_PASSWORD=CompanyPass456
YESCAPTCHA_API_KEY=xyz789abc123
ATLASSIAN_SITE_NAME=company-devai
CAPTCHA_TIMEOUT=180
REQUEST_DELAY=3.0
MANUAL_VERIFICATION=true
DEBUG_MODE=true
```

运行：
```bash
uv run atlassian-register --config my-config.env
```

### 示例 3: 调试模式

```bash
uv run atlassian-register \
  --config .env \
  --manual-verification \
  --debug \
  --verbose \
  --output registration_result.json
```

### 示例 4: Python API 使用

```python
from atlassian_register import RegistrationConfig, RegistrationFlow

# 创建配置
config = RegistrationConfig(
    email="<EMAIL>",
    password="password123",
    yescaptcha_api_key="your-api-key",
    site_name="my-site",
    manual_verification=True,
    debug_mode=True
)

# 运行注册
flow = RegistrationFlow(config)
try:
    result = flow.run_registration()
    
    if result.success:
        print(f"✅ 注册成功!")
        print(f"账户ID: {result.account_id}")
        print(f"API Token: {result.api_token}")
        print(f"站点URL: {result.site_url}")
        
        # 保存结果
        result.save_to_file(Path("result.json"))
    else:
        print(f"❌ 注册失败: {result.error_message}")
        
finally:
    flow.close()
```

## 输出格式

### 成功输出

```
✅ Registration completed successfully!
Account ID: 5f8a9b2c1d3e4f5g6h7i8j9k
API Token: ATATT3xFfGF0T...
Site URL: https://my-site.atlassian.net
```

### 错误输出

```
❌ Registration failed!
Error: CAPTCHA solving failed: Insufficient balance
```

### JSON 输出文件

```json
{
  "success": true,
  "account_id": "5f8a9b2c1d3e4f5g6h7i8j9k",
  "api_token": "ATATT3xFfGF0T...",
  "site_url": "https://my-site.atlassian.net",
  "site_name": "my-site",
  "org_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
  "timestamp": "2024-01-15T10:30:45.123456",
  "duration_seconds": 45.67
}
```

## 故障排除

### 常见问题

#### 1. CAPTCHA 求解失败

**错误**: `CAPTCHA solving failed: Insufficient balance`

**解决方案**:
- 检查 YesCaptcha 账户余额
- 验证 API 密钥是否正确
- 增加 `CAPTCHA_TIMEOUT` 值

#### 2. 网络连接问题

**错误**: `Network error while creating CAPTCHA task`

**解决方案**:
- 检查网络连接
- 配置代理设置
- 增加 `MAX_RETRIES` 和 `RETRY_DELAY`

#### 3. 邮箱格式错误

**错误**: `Email format is invalid`

**解决方案**:
- 确保邮箱地址格式正确
- 使用真实的邮箱地址

#### 4. 密码强度不足

**错误**: `Password must be at least 8 characters long`

**解决方案**:
- 使用至少8位字符的密码
- 包含字母、数字和特殊字符

### 调试技巧

#### 启用详细日志

```bash
uv run atlassian-register --config .env --debug --verbose
```

#### 使用手动验证

```bash
uv run atlassian-register --config .env --manual-verification
```

#### 检查请求日志

```env
ENABLE_REQUEST_LOGGING=true
REQUEST_LOG_FILE=debug_requests.log
```

#### 验证包格式

```python
from atlassian_register.verification import PacketVerifier

verifier = PacketVerifier(config)
result = verifier.verify_request_format(request_data, "147_c.txt")
print(result)
```

## 安全考虑

### 1. 凭据保护

- 永远不要在代码中硬编码密码
- 使用环境变量或安全的配置文件
- 定期轮换 API 密钥

### 2. 网络安全

- 考虑使用代理服务
- 启用请求延迟以避免被检测
- 监控请求频率

### 3. 数据保护

- 及时删除包含敏感信息的日志文件
- 不要提交包含真实凭据的配置文件
- 使用 `.gitignore` 保护敏感文件

### 4. 合规性

- 确保遵守 Atlassian 服务条款
- 仅用于合法的自动化目的
- 尊重速率限制和使用政策

## 性能优化

### 1. 请求优化

```env
REQUEST_DELAY=1.0          # 减少延迟（谨慎使用）
MAX_RETRIES=5              # 增加重试次数
CAPTCHA_TIMEOUT=180        # 增加CAPTCHA超时
```

### 2. 并发控制

- 避免同时运行多个实例
- 使用适当的请求间隔
- 监控API配额使用情况

### 3. 资源管理

```python
# 确保正确关闭资源
try:
    flow = RegistrationFlow(config)
    result = flow.run_registration()
finally:
    flow.close()
```
