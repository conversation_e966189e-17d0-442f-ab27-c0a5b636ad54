# Atlassian 注册流程包分析结果（修正版）

下面按时序把抓包里**真正和业务逻辑相关**的往返（request / response）挑出来，列出重点字段，最后给出可脚本化的思路。
（包号均对应 `atlassian‑register.zip/raw/XXX_[c|s].txt` 中的文件，方便你自己再去核对）

**⚠️ 重要更新**：经过深入包分析，发现实际注册流程与初步分析有重大差异，以下为修正后的准确流程。

---

## 1 注册页面初始化

| 步   | 报文              | 方法 & URL                                  | 关键要点                                                                                                                                   |
| --- | --------------- | ----------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------- |
| 1‑a | 147\_c / 147\_s | **GET `https://id.atlassian.com/signup`** | 200 OK，下载表单 HTML。响应里下发了第一批 Cookie（`atlassian.account.xsrf.token`、`ajs_anonymous_id`、`atlassian.account.ffs.id` 等）。这些后续每个调用都要随 Cookie 头一起带上。 |

**重要发现**：`atlassian.account.xsrf.token` 在不同端点使用不同的头部名称：
- 邮箱验证时使用 `x-csrf-token` 头部
- 站点操作时使用 `xsrf-token` 头部  
- API令牌创建时不需要显式XSRF头部

---

## 2 实际注册流程（重大修正）

**修正说明**：225_c.txt 实际上是analytics追踪请求，不是注册请求。真正的注册在243_c.txt。

| 步   | 报文              | 方法 & URL                                                       | Payload 摘要                                                                                                                                                                                            |
| --- | --------------- | -------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 2‑a | 243\_c / 243\_s | **POST `https://id.atlassian.com/rest/signup`** | `content‑type: application/json`，包含：<br>• `email`: "<EMAIL>"<br>• `challenge-response`: reCAPTCHA token<br>• `castleToken`: 反欺诈令牌<br>• `display`: null（注意不是displayName）|
| 2‑b | 225\_c / 225\_s | **POST `/gateway/api/gasv3/api/v1/batch`** (analytics only) | 这是analytics追踪请求，记录reCAPTCHA加载事件，不是实际注册。`content‑type: text/plain`，包含analytics数据。 |

> ⚠️ **关键发现**：
> 1. 实际注册使用REST API (`/rest/signup`)，不是GraphQL batch
> 2. 需要Castle.js反欺诈令牌，这是复杂的浏览器指纹识别
> 3. reCAPTCHA使用"score"类型，不是传统的图像验证

---

## 3 邮箱验证（重大修正）

**修正说明**：254_c.txt 是压缩响应，真正的邮箱验证在257_c.txt。

| 步   | 报文              | 方法 & URL                                                       | Payload 摘要                                                                                                                                                                                            |
| --- | --------------- | -------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 3‑a | 257\_c / 257\_s | **POST `https://id.atlassian.com/rest/signup/verify-email/otp`** | `content‑type: application/json`，包含：<br>• `otpCode`: "W9L6WE"（邮箱验证码）<br>• `token`: JWT令牌（从注册响应获得）<br>**重要**：使用`x-csrf-token`头部 |

> ⚠️ **关键发现**：
> 1. 使用REST API (`/rest/signup/verify-email/otp`)，不是GraphQL batch
> 2. 需要注册时返回的JWT token
> 3. 使用`x-csrf-token`头部，不是`x-atlassian-token`

---

## 4 API令牌创建

| 步   | 报文              | 方法 & URL                                                       | Payload 摘要                                                                                                                                                                                            |
| --- | --------------- | -------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 4‑a | 727\_c / 727\_s | **POST `https://id.atlassian.com/manage/api-tokens`** | `content‑type: application/json`，包含：<br>• `label`: "rovo"<br>• `expiry`: "2026-06-16T00:00:00.000Z"<br>**注意**：无需显式XSRF头部 |

返回的API令牌格式：`ATATT3xFfGF0_Ae-4tlj0...`

---

## 5 站点名称推荐

| 步   | 报文              | 方法 & URL                                                       | Payload 摘要                                                                                                                                                                                            |
| --- | --------------- | -------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 5‑a | 1048\_c / 1048\_s | **POST `https://www.atlassian.com/site/recommended-name`** | `content‑type: application/json`，包含：<br>• `email`: "<EMAIL>"<br>**重要**：使用`xsrf-token`头部（不同于id.atlassian.com） |

返回推荐的站点名称，如：`wendavid-team-y60a2ps6`

---

## 6 DevAI站点创建

| 步   | 报文              | 方法 & URL                                                       | Payload 摘要                                                                                                                                                                                            |
| --- | --------------- | -------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 6‑a | 1070\_c / 1070\_s | **POST `https://www.atlassian.com/activate-product`** | `content‑type: application/json`，包含：<br>• `siteName`: "wendavid-team-y60a2ps6"<br>• `ccpProducts`: DevAI产品配置<br>• `gRecaptchaResponse`: 第二个reCAPTCHA<br>• `consent`: 同意条款 |

**DevAI产品配置**：
- `offeringId`: "d69d4faa-7677-48f2-a432-fda525d2e223"
- `productKey`: "devai.ondemand"
- `edition`: "free"

---

## 脚本化实现要点

### 1. 会话管理
- 维护完整的cookie jar
- 处理两个域名间的cookie同步：`id.atlassian.com` ↔ `www.atlassian.com`
- 正确处理不同的XSRF token头部名称

### 2. 反欺诈和验证
- **Castle Token生成**：需要实现复杂的浏览器指纹识别
- **双重reCAPTCHA**：注册和站点创建各需要一次
- **JWT Token流程**：注册→验证→API令牌创建

### 3. 关键端点
1. `POST /rest/signup` - 实际注册（需Castle token + reCAPTCHA）
2. `POST /rest/signup/verify-email/otp` - 邮箱验证（需JWT token）
3. `POST /manage/api-tokens` - API令牌创建
4. `POST /site/recommended-name` - 站点名称推荐
5. `POST /activate-product` - DevAI站点创建

### 4. 错误处理
- Castle token验证失败
- reCAPTCHA验证失败
- 邮箱验证码错误
- 站点名称冲突
- 产品配置错误

这个修正版本基于对实际包文件的深入分析，提供了准确的技术实现路径。
