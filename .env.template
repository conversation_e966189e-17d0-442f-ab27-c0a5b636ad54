# Atlassian Registration Configuration Template
# Copy this file to .env and fill in your values

# Required: Email and password for registration
ATLASSIAN_EMAIL=<EMAIL>
ATLASSIAN_PASSWORD=your-secure-password

# Optional: Site name (if not provided, will use recommended name)
ATLASSIAN_SITE_NAME=

# Required: YesCaptcha API configuration
YESCAPTCHA_API_KEY=your-yescaptcha-api-key

# Optional: CAPTCHA solving timeout (seconds)
CAPTCHA_TIMEOUT=120

# Optional: Request delays and rate limiting
REQUEST_DELAY=2.0
MAX_RETRIES=3
RETRY_DELAY=5.0

# Optional: Manual verification and debugging
MANUAL_VERIFICATION=false
DEBUG_MODE=false
VERBOSE_LOGGING=false

# Optional: Output configuration
OUTPUT_FILE=registration_result.json

# Optional: User agent and browser fingerprinting
USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/120.0.0.0 Safari/537.36
BROWSER_LANGUAGE=en-US,en;q=0.9
BROWSER_TIMEZONE=America/New_York

# Optional: Proxy configuration (if needed)
HTTP_PROXY=
HTTPS_PROXY=

# Optional: Advanced settings
ENABLE_COOKIES_PERSISTENCE=true
COOKIES_FILE=.cookies.json
ENABLE_REQUEST_LOGGING=false
REQUEST_LOG_FILE=requests.log
