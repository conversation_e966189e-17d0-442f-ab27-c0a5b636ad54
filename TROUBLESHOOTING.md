# 故障排除指南

## 常见错误及解决方案

### 配置相关错误

#### 错误: `Email format is invalid`
**原因**: 邮箱地址格式不正确
**解决方案**:
```bash
# 确保邮箱格式正确
ATLASSIAN_EMAIL=<EMAIL>  # ✅ 正确
ATLASSIAN_EMAIL=user@example      # ❌ 错误
ATLASSIAN_EMAIL=user.example.com  # ❌ 错误
```

#### 错误: `Password must be at least 8 characters long`
**原因**: 密码长度不足
**解决方案**:
```bash
# 使用至少8位字符的密码
ATLASSIAN_PASSWORD=mypass123      # ✅ 正确 (8位)
ATLASSIAN_PASSWORD=pass           # ❌ 错误 (4位)
```

#### 错误: `YesCaptcha API key is required`
**原因**: 缺少或无效的 YesCaptcha API 密钥
**解决方案**:
1. 注册 YesCaptcha 账户
2. 获取 API 密钥
3. 设置环境变量:
```bash
YESCAPTCHA_API_KEY=your-actual-api-key
```

### CAPTCHA 相关错误

#### 错误: `CAPTCHA solving failed: Insufficient balance`
**原因**: YesCaptcha 账户余额不足
**解决方案**:
1. 检查账户余额:
```python
from atlassian_register.captcha import CaptchaSolver
solver = CaptchaSolver(config)
balance = solver.get_balance()
print(f"余额: ${balance}")
```
2. 充值 YesCaptcha 账户

#### 错误: `CAPTCHA solving timeout after 120 seconds`
**原因**: CAPTCHA 求解超时
**解决方案**:
1. 增加超时时间:
```bash
CAPTCHA_TIMEOUT=300  # 5分钟
```
2. 检查 YesCaptcha 服务状态
3. 重试操作

#### 错误: `Failed to create CAPTCHA task: ERROR_KEY_DOES_NOT_EXIST`
**原因**: API 密钥无效
**解决方案**:
1. 验证 API 密钥是否正确
2. 检查密钥是否已激活
3. 联系 YesCaptcha 支持

### 网络相关错误

#### 错误: `Network error while creating CAPTCHA task`
**原因**: 网络连接问题
**解决方案**:
1. 检查网络连接
2. 配置代理:
```bash
HTTP_PROXY=http://proxy.example.com:8080
HTTPS_PROXY=https://proxy.example.com:8080
```
3. 增加重试次数:
```bash
MAX_RETRIES=5
RETRY_DELAY=10.0
```

#### 错误: `Request failed after all retries`
**原因**: 多次重试后仍然失败
**解决方案**:
1. 检查目标服务器状态
2. 增加重试配置:
```bash
MAX_RETRIES=10
RETRY_DELAY=15.0
REQUEST_DELAY=5.0
```
3. 使用不同的网络环境

### 注册流程错误

#### 错误: `Failed to get signup page`
**原因**: 无法访问注册页面
**解决方案**:
1. 检查 Atlassian 服务状态
2. 验证网络连接
3. 尝试使用代理
4. 检查防火墙设置

#### 错误: `Account registration failed`
**原因**: 账户注册失败
**可能原因及解决方案**:
1. **邮箱已被使用**:
   - 使用不同的邮箱地址
   - 检查是否已有账户

2. **密码不符合要求**:
   - 使用更强的密码
   - 包含大小写字母、数字和特殊字符

3. **CAPTCHA 验证失败**:
   - 检查 CAPTCHA token 是否有效
   - 重新求解 CAPTCHA

#### 错误: `Email verification failed`
**原因**: 邮箱验证失败
**解决方案**:
1. 检查验证 token 是否正确
2. 确保在有效时间内验证
3. 检查邮箱是否可访问

#### 错误: `API token creation failed`
**原因**: API token 创建失败
**解决方案**:
1. 确保账户已完全激活
2. 检查账户权限
3. 重试操作

#### 错误: `Site creation failed`
**原因**: 站点创建失败
**可能原因及解决方案**:
1. **站点名已被使用**:
   - 使用不同的站点名
   - 让系统自动生成站点名

2. **第二次 CAPTCHA 失败**:
   - 重新求解 CAPTCHA
   - 检查 YesCaptcha 余额

## 调试技巧

### 1. 启用详细日志

```bash
# 启用所有调试选项
uv run atlassian-register \
  --config .env \
  --debug \
  --verbose \
  --manual-verification
```

### 2. 使用手动验证检查点

```bash
# 在每个步骤暂停以检查数据
uv run atlassian-register --config .env --manual-verification
```

### 3. 检查请求日志

```bash
# 启用请求日志记录
ENABLE_REQUEST_LOGGING=true
REQUEST_LOG_FILE=debug_requests.log
```

### 4. 验证包格式

```python
from atlassian_register.verification import PacketVerifier, DebugOutputHelper

config = RegistrationConfig(...)
verifier = PacketVerifier(config)

# 验证请求格式
request_data = {
    "method": "POST",
    "url": "https://id.atlassian.com/rest/signup",
    "headers": {...},
    "body": {...}
}

result = verifier.verify_request_format(request_data, "243_c.txt")
formatted = DebugOutputHelper.format_verification_result(result)
print(formatted)
```

### 5. 测试单个组件

```python
# 测试 CAPTCHA 求解
from atlassian_register.captcha import CaptchaSolver

solver = CaptchaSolver(config)
try:
    token = solver.solve_recaptcha(
        site_url="https://id.atlassian.com/signup",
        site_key="6LcmDiATAAAAAOcMv1gEi85kHPcTXHlB3KpzMXcH"
    )
    print(f"CAPTCHA token: {token}")
except Exception as e:
    print(f"CAPTCHA 错误: {e}")

# 测试 Castle token 生成
from atlassian_register.castle import CastleTokenGenerator

generator = CastleTokenGenerator(config)
castle_token = generator.generate_token()
print(f"Castle token: {castle_token[:50]}...")
```

## 性能问题

### 1. 运行缓慢

**原因**: 请求延迟过长
**解决方案**:
```bash
# 减少延迟（谨慎使用）
REQUEST_DELAY=1.0
CAPTCHA_TIMEOUT=60
```

### 2. 内存使用过高

**原因**: 资源未正确释放
**解决方案**:
```python
# 确保正确关闭资源
try:
    flow = RegistrationFlow(config)
    result = flow.run_registration()
finally:
    flow.close()
```

### 3. CAPTCHA 求解时间过长

**原因**: YesCaptcha 服务繁忙
**解决方案**:
1. 使用更高优先级的 YesCaptcha 服务
2. 增加超时时间
3. 在非高峰时段运行

## 环境问题

### 1. Python 版本不兼容

**错误**: `requires-python = ">=3.13"`
**解决方案**:
1. 升级到 Python 3.13+
2. 使用 pyenv 管理 Python 版本:
```bash
pyenv install 3.13.0
pyenv local 3.13.0
```

### 2. uv 包管理器问题

**错误**: `command not found: uv`
**解决方案**:
1. 安装 uv:
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```
2. 或使用 pip:
```bash
pip install uv
```

### 3. 依赖冲突

**错误**: 包版本冲突
**解决方案**:
1. 清理虚拟环境:
```bash
rm -rf .venv
uv venv
```
2. 重新安装依赖:
```bash
uv add requests python-dotenv urllib3
uv pip install -e .
```

## 获取帮助

### 1. 查看帮助信息

```bash
uv run atlassian-register --help
```

### 2. 运行集成测试

```bash
uv run python test_integration.py
```

### 3. 检查配置

```bash
# 干运行模式验证配置
uv run atlassian-register --config .env --dry-run --verbose
```

### 4. 联系支持

如果问题仍然存在，请提供以下信息：
- 错误消息的完整输出
- 使用的配置（隐藏敏感信息）
- Python 和 uv 版本
- 操作系统信息
- 重现步骤

### 5. 常用诊断命令

```bash
# 检查 Python 版本
python --version

# 检查 uv 版本
uv --version

# 检查已安装的包
uv pip list

# 检查项目结构
tree atlassian_register/

# 测试基本导入
uv run python -c "import atlassian_register; print('导入成功')"
```
