"""
CAPTCHA solving integration for Atlassian Register.

This module provides integration with YesCaptcha service for solving
reCAPTCHA challenges during the registration process.
"""

import requests
import time
import logging
from typing import Dict, Any, Optional
from .config import RegistrationConfig, CaptchaError


logger = logging.getLogger(__name__)


class CaptchaSolver:
    """CAPTCHA solver using YesCaptcha service."""
    
    # YesCaptcha API endpoints
    CREATE_TASK_URL = "https://api.yescaptcha.com/createTask"
    GET_RESULT_URL = "https://api.yescaptcha.com/getTaskResult"
    
    def __init__(self, config: RegistrationConfig):
        """Initialize the CAPTCHA solver with configuration."""
        self.config = config
        self.api_key = config.yescaptcha_api_key
        self.timeout = config.captcha_timeout
        
        # Validate API key
        if not self.api_key:
            raise CaptchaError("YesCaptcha API key is required")
    
    def solve_recaptcha(self, site_url: str, site_key: str, 
                       action: Optional[str] = None, 
                       min_score: float = 0.3) -> str:
        """
        Solve reCAPTCHA v3 challenge.
        
        Args:
            site_url: The URL where the reCAPTCHA is located
            site_key: The reCAPTCHA site key
            action: The action for reCAPTCHA v3 (optional)
            min_score: Minimum score for reCAPTCHA v3 (default: 0.3)
            
        Returns:
            The reCAPTCHA token
            
        Raises:
            CaptchaError: If CAPTCHA solving fails
        """
        logger.info(f"Starting reCAPTCHA solving for {site_url}")
        
        # Create task
        task_id = self._create_task(site_url, site_key, action, min_score)
        
        # Wait for solution
        token = self._wait_for_solution(task_id)
        
        logger.info("reCAPTCHA solved successfully")
        return token
    
    def _create_task(self, site_url: str, site_key: str, 
                    action: Optional[str], min_score: float) -> str:
        """Create a reCAPTCHA solving task."""
        task_data = {
            "type": "ReCaptchaV3TaskProxyless",
            "websiteURL": site_url,
            "websiteKey": site_key,
            "minScore": min_score,
            "pageAction": action or "submit"
        }
        
        payload = {
            "clientKey": self.api_key,
            "task": task_data
        }
        
        try:
            response = requests.post(self.CREATE_TASK_URL, json=payload, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get("errorId") == 0:
                task_id = result.get("taskId")
                if not task_id:
                    raise CaptchaError("No task ID returned from YesCaptcha")
                
                logger.debug(f"Created CAPTCHA task: {task_id}")
                return str(task_id)
            else:
                error_code = result.get("errorCode", "UNKNOWN")
                error_description = result.get("errorDescription", "Unknown error")
                raise CaptchaError(f"Failed to create CAPTCHA task: {error_code} - {error_description}")
                
        except requests.exceptions.RequestException as e:
            raise CaptchaError(f"Network error while creating CAPTCHA task: {str(e)}")
        except Exception as e:
            raise CaptchaError(f"Unexpected error while creating CAPTCHA task: {str(e)}")
    
    def _wait_for_solution(self, task_id: str) -> str:
        """Wait for CAPTCHA solution with polling."""
        start_time = time.time()
        poll_interval = 5  # Start with 5 seconds
        max_poll_interval = 30  # Maximum 30 seconds between polls
        
        logger.debug(f"Waiting for CAPTCHA solution (task: {task_id}, timeout: {self.timeout}s)")
        
        while time.time() - start_time < self.timeout:
            try:
                payload = {
                    "clientKey": self.api_key,
                    "taskId": task_id
                }
                
                response = requests.post(self.GET_RESULT_URL, json=payload, timeout=30)
                response.raise_for_status()
                
                result = response.json()
                
                if result.get("errorId") == 0:
                    status = result.get("status")
                    
                    if status == "ready":
                        solution = result.get("solution", {})
                        token = solution.get("gRecaptchaResponse")
                        
                        if not token:
                            raise CaptchaError("No reCAPTCHA token in solution")
                        
                        elapsed = time.time() - start_time
                        logger.debug(f"CAPTCHA solved in {elapsed:.1f} seconds")
                        return token
                        
                    elif status == "processing":
                        logger.debug(f"CAPTCHA still processing... (elapsed: {time.time() - start_time:.1f}s)")
                        time.sleep(poll_interval)
                        
                        # Gradually increase poll interval
                        poll_interval = min(poll_interval + 2, max_poll_interval)
                        continue
                        
                    else:
                        raise CaptchaError(f"Unexpected CAPTCHA status: {status}")
                        
                else:
                    error_code = result.get("errorCode", "UNKNOWN")
                    error_description = result.get("errorDescription", "Unknown error")
                    raise CaptchaError(f"CAPTCHA solving error: {error_code} - {error_description}")
                    
            except requests.exceptions.RequestException as e:
                logger.warning(f"Network error while polling CAPTCHA result: {e}")
                time.sleep(poll_interval)
                continue
                
        raise CaptchaError(f"CAPTCHA solving timeout after {self.timeout} seconds")
    
    def get_balance(self) -> float:
        """Get account balance from YesCaptcha."""
        payload = {
            "clientKey": self.api_key
        }
        
        try:
            response = requests.post("https://api.yescaptcha.com/getBalance", json=payload, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get("errorId") == 0:
                balance = result.get("balance", 0.0)
                logger.debug(f"YesCaptcha balance: ${balance}")
                return float(balance)
            else:
                error_code = result.get("errorCode", "UNKNOWN")
                error_description = result.get("errorDescription", "Unknown error")
                raise CaptchaError(f"Failed to get balance: {error_code} - {error_description}")
                
        except requests.exceptions.RequestException as e:
            raise CaptchaError(f"Network error while getting balance: {str(e)}")
        except Exception as e:
            raise CaptchaError(f"Unexpected error while getting balance: {str(e)}")


def extract_recaptcha_site_key(html_content: str) -> Optional[str]:
    """
    Extract reCAPTCHA site key from HTML content.
    
    Args:
        html_content: HTML content to search
        
    Returns:
        The site key if found, None otherwise
    """
    import re
    
    # Common patterns for reCAPTCHA site key
    patterns = [
        r'data-sitekey=["\']([^"\']+)["\']',
        r'sitekey["\']?\s*:\s*["\']([^"\']+)["\']',
        r'grecaptcha\.render\([^,]+,\s*{\s*["\']?sitekey["\']?\s*:\s*["\']([^"\']+)["\']',
        r'window\.recaptchaSiteKey\s*=\s*["\']([^"\']+)["\']'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, html_content, re.IGNORECASE)
        if match:
            site_key = match.group(1)
            logger.debug(f"Found reCAPTCHA site key: {site_key}")
            return site_key
    
    logger.warning("No reCAPTCHA site key found in HTML content")
    return None
