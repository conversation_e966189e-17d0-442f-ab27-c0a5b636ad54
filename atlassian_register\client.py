"""
HTTP client for Atlassian API interactions.

This module provides the main HTTP client class for handling
requests to Atlassian services with proper session and cookie management.
"""

import requests
import json
import time
import logging
from typing import Dict, Any, Optional, List
from urllib.parse import urljoin, urlparse
from .config import RegistrationConfig, NetworkError


logger = logging.getLogger(__name__)


class AtlassianClient:
    """HTTP client for Atlassian API interactions."""

    # Base URLs for different Atlassian services
    ID_BASE_URL = "https://id.atlassian.com"
    WWW_BASE_URL = "https://www.atlassian.com"

    def __init__(self, config: RegistrationConfig):
        """Initialize the client with configuration."""
        self.config = config
        self.session = requests.Session()
        self.cookies = {}
        self._setup_session()

        # Request logging
        if config.enable_request_logging:
            self._setup_request_logging()

    def _setup_session(self) -> None:
        """Setup the requests session with proper headers and configuration."""
        # Base headers based on packet analysis (147_c.txt)
        self.session.headers.update({
            'User-Agent': self.config.user_agent,
            'Accept-Language': self.config.browser_language,
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-User': '?1',
            'Sec-Fetch-Dest': 'document',
            'Priority': 'u=0, i'
        })

        # Proxy configuration
        if self.config.http_proxy or self.config.https_proxy:
            proxies = {}
            if self.config.http_proxy:
                proxies['http'] = self.config.http_proxy
            if self.config.https_proxy:
                proxies['https'] = self.config.https_proxy
            self.session.proxies.update(proxies)

        # Load persistent cookies if enabled
        if self.config.enable_cookies_persistence:
            self._load_cookies()

    def _setup_request_logging(self) -> None:
        """Setup request/response logging."""
        # Enable detailed logging for requests
        import http.client as http_client
        http_client.HTTPConnection.debuglevel = 1

        # Configure logging
        requests_log = logging.getLogger("requests.packages.urllib3")
        requests_log.setLevel(logging.DEBUG)
        requests_log.propagate = True
    
    def _load_cookies(self) -> None:
        """Load cookies from persistent storage."""
        try:
            import os
            if os.path.exists(self.config.cookies_file):
                with open(self.config.cookies_file, 'r') as f:
                    cookies_data = json.load(f)
                    for name, value in cookies_data.items():
                        self.session.cookies.set(name, value)
                logger.debug(f"Loaded {len(cookies_data)} cookies from {self.config.cookies_file}")
        except Exception as e:
            logger.warning(f"Failed to load cookies: {e}")

    def _save_cookies(self) -> None:
        """Save cookies to persistent storage."""
        if not self.config.enable_cookies_persistence:
            return

        try:
            cookies_data = {cookie.name: cookie.value for cookie in self.session.cookies}
            with open(self.config.cookies_file, 'w') as f:
                json.dump(cookies_data, f, indent=2)
            logger.debug(f"Saved {len(cookies_data)} cookies to {self.config.cookies_file}")
        except Exception as e:
            logger.warning(f"Failed to save cookies: {e}")

    def _extract_cookies_from_response(self, response: requests.Response) -> Dict[str, str]:
        """Extract cookies from response headers."""
        cookies = {}
        for cookie in response.cookies:
            cookies[cookie.name] = cookie.value
            logger.debug(f"Extracted cookie: {cookie.name}={cookie.value}")
        return cookies

    def _make_request(self, method: str, url: str, **kwargs) -> requests.Response:
        """Make HTTP request with error handling and rate limiting."""
        # Apply request delay
        if self.config.request_delay > 0:
            time.sleep(self.config.request_delay)

        # Retry logic
        last_exception = None
        for attempt in range(self.config.max_retries + 1):
            try:
                logger.debug(f"Making {method} request to {url} (attempt {attempt + 1})")
                response = self.session.request(method, url, **kwargs)

                # Extract and save cookies
                self._extract_cookies_from_response(response)
                self._save_cookies()

                # Check for rate limiting
                if response.status_code == 429:
                    retry_after = int(response.headers.get('Retry-After', self.config.retry_delay))
                    logger.warning(f"Rate limited, waiting {retry_after} seconds")
                    time.sleep(retry_after)
                    continue

                # Log response details
                logger.debug(f"Response status: {response.status_code}")
                logger.debug(f"Response headers: {dict(response.headers)}")

                return response

            except requests.exceptions.RequestException as e:
                last_exception = e
                if attempt < self.config.max_retries:
                    wait_time = self.config.retry_delay * (2 ** attempt)  # Exponential backoff
                    logger.warning(f"Request failed (attempt {attempt + 1}): {e}, retrying in {wait_time}s")
                    time.sleep(wait_time)
                else:
                    logger.error(f"Request failed after {self.config.max_retries + 1} attempts: {e}")

        raise NetworkError(f"Request failed after all retries: {last_exception}")

    def get_signup_page(self) -> Dict[str, Any]:
        """Get the signup page and extract initial cookies."""
        # Based on 147_c.txt analysis
        url = f"{self.ID_BASE_URL}/signup"

        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-User': '?1',
            'Sec-Fetch-Dest': 'document',
            'Upgrade-Insecure-Requests': '1'
        }

        try:
            # Step 1: Get the signup page (147_c.txt)
            response = self._make_request('GET', url, headers=headers)

            if response.status_code != 200:
                return {
                    'success': False,
                    'status_code': response.status_code,
                    'error': f"Signup page request failed with status {response.status_code}"
                }

            # Extract initial cookies (usually none from this request)
            initial_cookies = self._extract_cookies_from_response(response)

            # Step 2: Get frontend state to obtain necessary cookies (159_c.txt)
            state_result = self._get_frontend_state()
            if not state_result.get('success'):
                logger.warning(f"Frontend state request failed: {state_result.get('error')}")
                # Continue anyway, as this might not be critical

            # Combine all cookies
            all_cookies = {**initial_cookies, **state_result.get('cookies', {})}

            return {
                'success': True,
                'status_code': response.status_code,
                'cookies': all_cookies,
                'content_length': len(response.content),
                'headers': dict(response.headers),
                'frontend_state': state_result
            }

        except Exception as e:
            logger.error(f"Failed to get signup page: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _get_frontend_state(self) -> Dict[str, Any]:
        """Get frontend state to obtain necessary cookies (159_c.txt)."""
        url = f"{self.ID_BASE_URL}/frontend/state"

        headers = {
            'Accept': '*/*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f"{self.ID_BASE_URL}/signup",
            'Priority': 'u=1, i'
        }

        try:
            response = self._make_request('GET', url, headers=headers)

            if response.status_code == 200:
                # Extract cookies from this response (should include xsrf token and ffs id)
                cookies = self._extract_cookies_from_response(response)

                # Try to parse JSON response
                try:
                    response_data = response.json()
                except json.JSONDecodeError:
                    response_data = {}

                return {
                    'success': True,
                    'status_code': response.status_code,
                    'cookies': cookies,
                    'data': response_data
                }
            else:
                return {
                    'success': False,
                    'status_code': response.status_code,
                    'error': f"Frontend state request failed with status {response.status_code}"
                }

        except Exception as e:
            logger.error(f"Failed to get frontend state: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def register_account(self, email: str, password: str, captcha_token: str, castle_token: str) -> Dict[str, Any]:
        """Register a new Atlassian account.

        Note: The password parameter is kept for API compatibility but is not used
        in the actual request, as Atlassian's registration endpoint does not include
        password in the initial signup request (verified from 243_c.txt packet analysis).
        """
        # Based on 243_c.txt analysis
        url = f"{self.ID_BASE_URL}/rest/signup"

        # Build the registration payload based on packet analysis (243_c.txt)
        # Note: Password is NOT included in the actual registration request
        payload = {
            "challenge-response": {
                "challenge-kind": "recaptcha",
                "token": {
                    "value": captcha_token
                },
                "type": "score"
            },
            "email": email,
            "display": None,
            "castleToken": castle_token
        }

        headers = {
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'Origin': self.ID_BASE_URL,
            'Referer': f"{self.ID_BASE_URL}/signup?email={email}",
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Priority': 'u=1, i'
        }

        try:
            response = self._make_request('POST', url, headers=headers, json=payload)

            if response.status_code == 200:
                try:
                    response_data = response.json()
                    return {
                        'success': True,
                        'status_code': response.status_code,
                        'data': response_data,
                        'verification_token': response_data.get('verificationToken'),
                        'account_id': response_data.get('accountId')
                    }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'status_code': response.status_code,
                        'error': 'Invalid JSON response'
                    }
            else:
                return {
                    'success': False,
                    'status_code': response.status_code,
                    'error': f"Registration failed with status {response.status_code}",
                    'response_text': response.text[:500]  # First 500 chars for debugging
                }

        except Exception as e:
            logger.error(f"Failed to register account: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def verify_email(self, verification_token: str) -> Dict[str, Any]:
        """Verify email address."""
        # Based on 257_c.txt analysis
        url = f"{self.ID_BASE_URL}/rest/signup/verify"

        payload = {
            "token": verification_token
        }

        headers = {
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'Origin': self.ID_BASE_URL,
            'Referer': f"{self.ID_BASE_URL}/signup/verify",
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'x-csrf-token': self.session.cookies.get('atlassian.account.xsrf.token', ''),
            'Priority': 'u=1, i'
        }

        try:
            response = self._make_request('POST', url, headers=headers, json=payload)

            if response.status_code == 200:
                try:
                    response_data = response.json()
                    return {
                        'success': True,
                        'status_code': response.status_code,
                        'data': response_data,
                        'account_id': response_data.get('accountId'),
                        'cloud_session_token': self.session.cookies.get('cloud.session.token')
                    }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'status_code': response.status_code,
                        'error': 'Invalid JSON response'
                    }
            else:
                return {
                    'success': False,
                    'status_code': response.status_code,
                    'error': f"Email verification failed with status {response.status_code}",
                    'response_text': response.text[:500]
                }

        except Exception as e:
            logger.error(f"Failed to verify email: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def create_api_token(self, label: str) -> Dict[str, Any]:
        """Create an API token."""
        # Based on 727_c.txt analysis
        url = f"{self.ID_BASE_URL}/rest/api-tokens"

        payload = {
            "label": label,
            "expiryDate": None  # No expiry date
        }

        headers = {
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'Origin': self.ID_BASE_URL,
            'Referer': f"{self.ID_BASE_URL}/manage-profile/security/api-tokens",
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Priority': 'u=1, i'
        }

        try:
            response = self._make_request('POST', url, headers=headers, json=payload)

            if response.status_code == 200:
                try:
                    response_data = response.json()
                    return {
                        'success': True,
                        'status_code': response.status_code,
                        'data': response_data,
                        'api_token': response_data.get('token'),
                        'token_id': response_data.get('id')
                    }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'status_code': response.status_code,
                        'error': 'Invalid JSON response'
                    }
            else:
                return {
                    'success': False,
                    'status_code': response.status_code,
                    'error': f"API token creation failed with status {response.status_code}",
                    'response_text': response.text[:500]
                }

        except Exception as e:
            logger.error(f"Failed to create API token: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_recommended_site_name(self) -> Dict[str, Any]:
        """Get recommended site name."""
        # Based on 1048_c.txt analysis - transition to www.atlassian.com
        url = f"{self.WWW_BASE_URL}/ex/jira/api/onboarding/cloud/site-name/recommendation"

        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Origin': self.WWW_BASE_URL,
            'Referer': f"{self.WWW_BASE_URL}/software/jira/guides/getting-started/overview",
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'x-csrf-token': self.session.cookies.get('atlassian.xsrf.token', ''),
            'Priority': 'u=1, i'
        }

        try:
            response = self._make_request('GET', url, headers=headers)

            if response.status_code == 200:
                try:
                    response_data = response.json()
                    return {
                        'success': True,
                        'status_code': response.status_code,
                        'data': response_data,
                        'recommended_name': response_data.get('siteName')
                    }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'status_code': response.status_code,
                        'error': 'Invalid JSON response'
                    }
            else:
                return {
                    'success': False,
                    'status_code': response.status_code,
                    'error': f"Site name recommendation failed with status {response.status_code}",
                    'response_text': response.text[:500]
                }

        except Exception as e:
            logger.error(f"Failed to get recommended site name: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def create_site(self, site_name: str, captcha_token: str) -> Dict[str, Any]:
        """Create a DevAI site."""
        # Based on 1070_c.txt analysis
        url = f"{self.WWW_BASE_URL}/ex/jira/api/onboarding/cloud/site"

        # Build the site creation payload for DevAI
        payload = {
            "siteName": site_name,
            "productBundleId": "devai",  # DevAI product bundle
            "challenge-response": {
                "challenge-kind": "recaptcha",
                "token": {
                    "value": captcha_token
                },
                "type": "score"
            },
            "consentManagement": {
                "analytics": True,
                "marketing": False
            },
            "timezone": self.config.browser_timezone,
            "language": "en"
        }

        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Origin': self.WWW_BASE_URL,
            'Referer': f"{self.WWW_BASE_URL}/software/jira/guides/getting-started/overview",
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'x-csrf-token': self.session.cookies.get('atlassian.xsrf.token', ''),
            'Priority': 'u=1, i'
        }

        try:
            response = self._make_request('POST', url, headers=headers, json=payload)

            if response.status_code == 200:
                try:
                    response_data = response.json()
                    return {
                        'success': True,
                        'status_code': response.status_code,
                        'data': response_data,
                        'site_url': response_data.get('siteUrl'),
                        'org_id': response_data.get('orgId'),
                        'site_name': response_data.get('siteName')
                    }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'status_code': response.status_code,
                        'error': 'Invalid JSON response'
                    }
            else:
                return {
                    'success': False,
                    'status_code': response.status_code,
                    'error': f"Site creation failed with status {response.status_code}",
                    'response_text': response.text[:500]
                }

        except Exception as e:
            logger.error(f"Failed to create site: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def close(self) -> None:
        """Close the client and clean up resources."""
        self.session.close()
        logger.debug("AtlassianClient closed")
