"""
Configuration management for Atlassian Register.

This module handles configuration loading, validation, and management
for the registration process.
"""

import os
import json
from dataclasses import dataclass, asdict
from typing import Optional, Dict, Any
from pathlib import Path
from dotenv import load_dotenv


class RegistrationError(Exception):
    """Base exception for registration errors."""
    pass


class ConfigurationError(RegistrationError):
    """Exception raised for configuration errors."""
    pass


class ValidationError(RegistrationError):
    """Exception raised for validation errors."""
    pass


class CaptchaError(RegistrationError):
    """Exception raised for CAPTCHA-related errors."""
    pass


class NetworkError(RegistrationError):
    """Exception raised for network-related errors."""
    pass


@dataclass
class RegistrationConfig:
    """Configuration for Atlassian registration process."""
    
    email: str
    password: str
    yescaptcha_api_key: str
    site_name: Optional[str] = None
    captcha_timeout: int = 120
    request_delay: float = 2.0
    max_retries: int = 3
    retry_delay: float = 5.0
    manual_verification: bool = False
    debug_mode: bool = False
    verbose_logging: bool = False
    user_agent: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    browser_language: str = "en-US,en;q=0.9"
    browser_timezone: str = "America/New_York"
    http_proxy: Optional[str] = None
    https_proxy: Optional[str] = None
    enable_cookies_persistence: bool = True
    cookies_file: str = ".cookies.json"
    enable_request_logging: bool = False
    request_log_file: str = "requests.log"
    
    @classmethod
    def from_args(cls, args) -> 'RegistrationConfig':
        """Create configuration from command line arguments."""
        # Load environment file if specified
        if hasattr(args, 'config') and args.config:
            load_dotenv(args.config)
        else:
            # Try to load default .env file
            load_dotenv()

        # Get values from args first, then environment variables
        email = args.email or os.getenv('ATLASSIAN_EMAIL', '')
        password = args.password or os.getenv('ATLASSIAN_PASSWORD', '')
        yescaptcha_api_key = getattr(args, 'yescaptcha_key', '') or os.getenv('YESCAPTCHA_API_KEY', '')
        site_name = getattr(args, 'site_name', None) or os.getenv('ATLASSIAN_SITE_NAME') or None
        captcha_timeout = getattr(args, 'captcha_timeout', None) or int(os.getenv('CAPTCHA_TIMEOUT', '120'))
        request_delay = getattr(args, 'delay', None) or float(os.getenv('REQUEST_DELAY', '2.0'))
        max_retries = int(os.getenv('MAX_RETRIES', '3'))
        retry_delay = float(os.getenv('RETRY_DELAY', '5.0'))
        manual_verification = getattr(args, 'manual_verification', False) or os.getenv('MANUAL_VERIFICATION', 'false').lower() == 'true'
        debug_mode = getattr(args, 'debug', False) or os.getenv('DEBUG_MODE', 'false').lower() == 'true'
        verbose_logging = getattr(args, 'verbose', False) or os.getenv('VERBOSE_LOGGING', 'false').lower() == 'true'
        user_agent = os.getenv('USER_AGENT', "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        browser_language = os.getenv('BROWSER_LANGUAGE', "en-US,en;q=0.9")
        browser_timezone = os.getenv('BROWSER_TIMEZONE', "America/New_York")
        http_proxy = os.getenv('HTTP_PROXY') or None
        https_proxy = os.getenv('HTTPS_PROXY') or None
        enable_cookies_persistence = os.getenv('ENABLE_COOKIES_PERSISTENCE', 'true').lower() == 'true'
        cookies_file = os.getenv('COOKIES_FILE', '.cookies.json')
        enable_request_logging = os.getenv('ENABLE_REQUEST_LOGGING', 'false').lower() == 'true'
        request_log_file = os.getenv('REQUEST_LOG_FILE', 'requests.log')

        return cls(
            email=email,
            password=password,
            yescaptcha_api_key=yescaptcha_api_key,
            site_name=site_name,
            captcha_timeout=captcha_timeout,
            request_delay=request_delay,
            max_retries=max_retries,
            retry_delay=retry_delay,
            manual_verification=manual_verification,
            debug_mode=debug_mode,
            verbose_logging=verbose_logging,
            user_agent=user_agent,
            browser_language=browser_language,
            browser_timezone=browser_timezone,
            http_proxy=http_proxy,
            https_proxy=https_proxy,
            enable_cookies_persistence=enable_cookies_persistence,
            cookies_file=cookies_file,
            enable_request_logging=enable_request_logging,
            request_log_file=request_log_file,
        )
    
    def validate(self) -> bool:
        """Validate configuration."""
        errors = []

        # Required fields validation
        if not self.email:
            errors.append("Email is required")
        elif '@' not in self.email or '.' not in self.email.split('@')[-1]:
            errors.append("Email format is invalid")

        if not self.password:
            errors.append("Password is required")
        elif len(self.password) < 8:
            errors.append("Password must be at least 8 characters long")

        if not self.yescaptcha_api_key:
            errors.append("YesCaptcha API key is required")

        # Numeric fields validation
        if self.captcha_timeout <= 0:
            errors.append("CAPTCHA timeout must be positive")
        if self.request_delay < 0:
            errors.append("Request delay cannot be negative")
        if self.max_retries < 0:
            errors.append("Max retries cannot be negative")
        if self.retry_delay < 0:
            errors.append("Retry delay cannot be negative")

        # File paths validation
        if self.enable_cookies_persistence:
            cookies_path = Path(self.cookies_file)
            if cookies_path.exists() and not cookies_path.is_file():
                errors.append(f"Cookies file path exists but is not a file: {self.cookies_file}")

        if self.enable_request_logging:
            log_path = Path(self.request_log_file)
            if log_path.exists() and not log_path.is_file():
                errors.append(f"Request log file path exists but is not a file: {self.request_log_file}")

        if errors:
            raise ConfigurationError(f"Configuration validation failed: {'; '.join(errors)}")

        return True

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary (excluding sensitive data)."""
        data = asdict(self)
        # Remove sensitive information
        data.pop('password', None)
        data.pop('yescaptcha_api_key', None)
        return data


@dataclass
class RegistrationResult:
    """Result of registration process."""

    success: bool
    account_id: Optional[str] = None
    api_token: Optional[str] = None
    site_url: Optional[str] = None
    site_name: Optional[str] = None
    org_id: Optional[str] = None
    error_message: Optional[str] = None
    error_code: Optional[str] = None
    timestamp: Optional[str] = None
    duration_seconds: Optional[float] = None

    def save_to_file(self, path: Path) -> None:
        """Save result to JSON file."""
        try:
            data = asdict(self)
            # Add timestamp if not present
            if not data.get('timestamp'):
                from datetime import datetime
                data['timestamp'] = datetime.now().isoformat()

            with open(path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            raise ConfigurationError(f"Failed to save result to file {path}: {str(e)}")

    @classmethod
    def from_file(cls, path: Path) -> 'RegistrationResult':
        """Load result from JSON file."""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return cls(**data)
        except Exception as e:
            raise ConfigurationError(f"Failed to load result from file {path}: {str(e)}")

    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary."""
        return asdict(self)

    @classmethod
    def success_result(cls, account_id: str, api_token: str, site_url: str,
                      site_name: Optional[str] = None, org_id: Optional[str] = None,
                      duration_seconds: Optional[float] = None) -> 'RegistrationResult':
        """Create a successful registration result."""
        from datetime import datetime
        return cls(
            success=True,
            account_id=account_id,
            api_token=api_token,
            site_url=site_url,
            site_name=site_name,
            org_id=org_id,
            timestamp=datetime.now().isoformat(),
            duration_seconds=duration_seconds
        )

    @classmethod
    def error_result(cls, error_message: str, error_code: Optional[str] = None,
                    duration_seconds: Optional[float] = None) -> 'RegistrationResult':
        """Create an error registration result."""
        from datetime import datetime
        return cls(
            success=False,
            error_message=error_message,
            error_code=error_code,
            timestamp=datetime.now().isoformat(),
            duration_seconds=duration_seconds
        )
