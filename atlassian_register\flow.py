"""
Main registration flow orchestration.

This module contains the main RegistrationFlow class that orchestrates
the complete Atlassian registration process.
"""

import time
import logging
from typing import Optional
from .config import RegistrationConfig, RegistrationResult
from .client import AtlassianClient
from .captcha import CaptchaSolver, extract_recaptcha_site_key
from .castle import CastleTokenGenerator


logger = logging.getLogger(__name__)


class RegistrationFlow:
    """Main registration flow orchestrator."""

    def __init__(self, config: RegistrationConfig):
        """Initialize the registration flow with configuration."""
        self.config = config
        self.client = AtlassianClient(config)
        self.captcha_solver = CaptchaSolver(config)
        self.castle_generator = CastleTokenGenerator(config)
        self.start_time = None
    
    def validate_configuration(self) -> RegistrationResult:
        """Validate configuration without performing registration."""
        try:
            self.config.validate()
            return RegistrationResult(
                success=True,
                error_message="Configuration validation successful"
            )
        except Exception as e:
            return RegistrationResult(
                success=False,
                error_message=f"Configuration validation failed: {str(e)}"
            )
    
    def run_registration(self, manual_verification: bool = False, delay: float = 2.0) -> RegistrationResult:
        """Run the complete registration process."""
        self.start_time = time.time()

        try:
            # Validate configuration
            self.config.validate()
            logger.info("Starting Atlassian registration process")

            # Step 1: Get signup page and extract initial cookies
            logger.info("Step 1: Getting signup page...")
            signup_result = self.client.get_signup_page()
            if not signup_result.get('success'):
                return RegistrationResult.error_result(
                    f"Failed to get signup page: {signup_result.get('error')}",
                    "SIGNUP_PAGE_ERROR",
                    self._get_elapsed_time()
                )

            if manual_verification:
                self._manual_verification_checkpoint("Signup page accessed", signup_result)

            # Step 2: Extract reCAPTCHA site key and solve CAPTCHA
            logger.info("Step 2: Extracting reCAPTCHA site key and solving CAPTCHA...")

            # Extract site key from signup page HTML
            site_key = None
            if signup_result.get('content_length', 0) > 0:
                # We need to get the HTML content to extract site key
                # Make another request to get uncompressed content
                try:
                    response = self.client._make_request('GET', f"{self.client.ID_BASE_URL}/signup",
                                                       headers={'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9'})
                    if response.status_code == 200:
                        from .captcha import extract_recaptcha_site_key
                        site_key = extract_recaptcha_site_key(response.text)
                        if site_key:
                            logger.info(f"Extracted reCAPTCHA site key: {site_key}")
                        else:
                            logger.warning("Could not extract site key from HTML, using fallback")
                except Exception as e:
                    logger.warning(f"Failed to extract site key: {e}")

            # Fallback to known site key if extraction fails
            if not site_key:
                site_key = "6LcmDiATAAAAAOcMv1gEi85kHPcTXHlB3KpzMXcH"
                logger.info(f"Using fallback site key: {site_key}")

            try:
                captcha_token = self.captcha_solver.solve_recaptcha(
                    site_url="https://id.atlassian.com/signup",
                    site_key=site_key,
                    action="signup",
                    min_score=0.3
                )
                logger.info("reCAPTCHA solved successfully")
            except Exception as e:
                return RegistrationResult.error_result(
                    f"CAPTCHA solving failed: {str(e)}",
                    "CAPTCHA_ERROR",
                    self._get_elapsed_time()
                )

            if manual_verification:
                self._manual_verification_checkpoint("CAPTCHA solved", {"token": captcha_token[:50] + "..."})

            # Step 3: Generate Castle token
            logger.info("Step 3: Generating Castle token...")
            castle_token = self.castle_generator.generate_token()

            if manual_verification:
                self._manual_verification_checkpoint("Castle token generated", {"token": castle_token[:50] + "..."})

            # Step 4: Register account
            logger.info("Step 4: Registering account...")
            register_result = self.client.register_account(
                email=self.config.email,
                password=self.config.password,
                captcha_token=captcha_token,
                castle_token=castle_token
            )

            if not register_result.get('success'):
                return RegistrationResult.error_result(
                    f"Account registration failed: {register_result.get('error')}",
                    "REGISTRATION_ERROR",
                    self._get_elapsed_time()
                )

            verification_token = register_result.get('verification_token')
            if not verification_token:
                return RegistrationResult.error_result(
                    "No verification token received from registration",
                    "VERIFICATION_TOKEN_ERROR",
                    self._get_elapsed_time()
                )

            if manual_verification:
                self._manual_verification_checkpoint("Account registered", register_result)

            # Step 5: Verify email
            logger.info("Step 5: Verifying email...")
            verify_result = self.client.verify_email(verification_token)

            if not verify_result.get('success'):
                return RegistrationResult.error_result(
                    f"Email verification failed: {verify_result.get('error')}",
                    "EMAIL_VERIFICATION_ERROR",
                    self._get_elapsed_time()
                )

            account_id = verify_result.get('account_id')
            if not account_id:
                return RegistrationResult.error_result(
                    "No account ID received from email verification",
                    "ACCOUNT_ID_ERROR",
                    self._get_elapsed_time()
                )

            if manual_verification:
                self._manual_verification_checkpoint("Email verified", verify_result)

            # Step 6: Create API token
            logger.info("Step 6: Creating API token...")
            token_result = self.client.create_api_token("Automated Registration Token")

            if not token_result.get('success'):
                return RegistrationResult.error_result(
                    f"API token creation failed: {token_result.get('error')}",
                    "API_TOKEN_ERROR",
                    self._get_elapsed_time()
                )

            api_token = token_result.get('api_token')
            if not api_token:
                return RegistrationResult.error_result(
                    "No API token received",
                    "API_TOKEN_MISSING_ERROR",
                    self._get_elapsed_time()
                )

            if manual_verification:
                self._manual_verification_checkpoint("API token created", {"token": api_token[:20] + "..."})

            # Step 7: Get recommended site name
            logger.info("Step 7: Getting recommended site name...")
            site_name = self.config.site_name
            if not site_name:
                site_name_result = self.client.get_recommended_site_name()
                if site_name_result.get('success'):
                    site_name = site_name_result.get('recommended_name', 'mysite')
                else:
                    site_name = 'mysite'  # Fallback

            if manual_verification:
                self._manual_verification_checkpoint("Site name determined", {"site_name": site_name})

            # Step 8: Extract site key and solve CAPTCHA for site creation
            logger.info("Step 8: Extracting site key and solving reCAPTCHA for site creation...")

            # Extract site key from site creation page
            site_creation_site_key = None
            try:
                response = self.client._make_request('GET',
                    "https://www.atlassian.com/software/jira/guides/getting-started/overview",
                    headers={'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9'})
                if response.status_code == 200:
                    from .captcha import extract_recaptcha_site_key
                    site_creation_site_key = extract_recaptcha_site_key(response.text)
                    if site_creation_site_key:
                        logger.info(f"Extracted site creation reCAPTCHA site key: {site_creation_site_key}")
                    else:
                        logger.warning("Could not extract site creation site key, using fallback")
            except Exception as e:
                logger.warning(f"Failed to extract site creation site key: {e}")

            # Fallback to known site key if extraction fails
            if not site_creation_site_key:
                site_creation_site_key = "6LcmDiATAAAAAOcMv1gEi85kHPcTXHlB3KpzMXcH"
                logger.info(f"Using fallback site creation site key: {site_creation_site_key}")

            try:
                site_captcha_token = self.captcha_solver.solve_recaptcha(
                    site_url="https://www.atlassian.com/software/jira/guides/getting-started/overview",
                    site_key=site_creation_site_key,
                    action="site_creation",
                    min_score=0.3
                )
            except Exception as e:
                return RegistrationResult.error_result(
                    f"Site creation CAPTCHA solving failed: {str(e)}",
                    "SITE_CAPTCHA_ERROR",
                    self._get_elapsed_time()
                )

            # Step 9: Create DevAI site
            logger.info("Step 9: Creating DevAI site...")
            site_result = self.client.create_site(site_name, site_captcha_token)

            if not site_result.get('success'):
                return RegistrationResult.error_result(
                    f"Site creation failed: {site_result.get('error')}",
                    "SITE_CREATION_ERROR",
                    self._get_elapsed_time()
                )

            site_url = site_result.get('site_url')
            org_id = site_result.get('org_id')

            if manual_verification:
                self._manual_verification_checkpoint("Site created", site_result)

            # Success!
            elapsed_time = self._get_elapsed_time()
            logger.info(f"Registration completed successfully in {elapsed_time:.1f} seconds")

            return RegistrationResult.success_result(
                account_id=account_id,
                api_token=api_token,
                site_url=site_url,
                site_name=site_name,
                org_id=org_id,
                duration_seconds=elapsed_time
            )

        except Exception as e:
            logger.error(f"Registration failed with exception: {e}")
            return RegistrationResult.error_result(
                f"Registration failed: {str(e)}",
                "UNEXPECTED_ERROR",
                self._get_elapsed_time()
            )
        finally:
            # Clean up
            self.client.close()

    def _get_elapsed_time(self) -> float:
        """Get elapsed time since registration started."""
        if self.start_time is None:
            return 0.0
        return time.time() - self.start_time

    def _manual_verification_checkpoint(self, step_name: str, data: dict) -> None:
        """Manual verification checkpoint for debugging."""
        if not self.config.manual_verification:
            return

        logger.info(f"=== MANUAL VERIFICATION CHECKPOINT: {step_name} ===")
        logger.info(f"Data: {data}")

        if self.config.debug_mode:
            print(f"\n=== MANUAL VERIFICATION CHECKPOINT ===")
            print(f"Step: {step_name}")
            print(f"Data: {data}")
            print("Press Enter to continue...")
            input()

    def close(self) -> None:
        """Close the registration flow and clean up resources."""
        if hasattr(self, 'client'):
            self.client.close()
