[project]
name = "atlassian-register"
version = "0.1.0"
description = "Automated Atlassian account registration and site creation tool"
readme = "README.md"
requires-python = ">=3.13"
authors = [
    {name = "Claude 4 Sonnet", email = "<EMAIL>"}
]
keywords = ["atlassian", "automation", "registration", "api"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.13",
]
dependencies = [
    "python-dotenv>=1.1.1",
    "requests>=2.32.4",
    "urllib3>=2.5.0",
]

[project.scripts]
atlassian-register = "atlassian_register.cli:main"

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
]

[tool.setuptools]
packages = ["atlassian_register"]

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[[tool.uv.index]]
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
default = true
