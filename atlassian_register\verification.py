"""
Manual verification system for Atlassian Register.

This module provides tools for manual verification of requests and responses
against packet captures for debugging and validation purposes.
"""

import json
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path
from .config import RegistrationConfig


logger = logging.getLogger(__name__)


class PacketVerifier:
    """Packet capture verification helper."""
    
    def __init__(self, config: RegistrationConfig):
        """Initialize the packet verifier."""
        self.config = config
        self.raw_dir = Path("raw")
    
    def verify_request_format(self, request_data: Dict[str, Any], 
                            packet_file: str) -> Dict[str, Any]:
        """
        Verify request format against packet capture.
        
        Args:
            request_data: The request data to verify
            packet_file: The packet capture file to compare against
            
        Returns:
            Verification result with details
        """
        try:
            packet_path = self.raw_dir / packet_file
            if not packet_path.exists():
                return {
                    "success": False,
                    "error": f"Packet file not found: {packet_file}"
                }
            
            # Read packet capture
            with open(packet_path, 'r', encoding='utf-8') as f:
                packet_content = f.read()
            
            # Parse packet content
            packet_data = self._parse_packet_content(packet_content)
            
            # Compare request data
            comparison = self._compare_requests(request_data, packet_data)
            
            return {
                "success": True,
                "packet_file": packet_file,
                "comparison": comparison,
                "packet_data": packet_data
            }
            
        except Exception as e:
            logger.error(f"Failed to verify request format: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def verify_response_format(self, response_data: Dict[str, Any], 
                             packet_file: str) -> Dict[str, Any]:
        """
        Verify response format against packet capture.
        
        Args:
            response_data: The response data to verify
            packet_file: The packet capture file to compare against
            
        Returns:
            Verification result with details
        """
        try:
            packet_path = self.raw_dir / packet_file
            if not packet_path.exists():
                return {
                    "success": False,
                    "error": f"Packet file not found: {packet_file}"
                }
            
            # Read packet capture
            with open(packet_path, 'r', encoding='utf-8') as f:
                packet_content = f.read()
            
            # Parse packet content
            packet_data = self._parse_packet_content(packet_content)
            
            # Compare response data
            comparison = self._compare_responses(response_data, packet_data)
            
            return {
                "success": True,
                "packet_file": packet_file,
                "comparison": comparison,
                "packet_data": packet_data
            }
            
        except Exception as e:
            logger.error(f"Failed to verify response format: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _parse_packet_content(self, content: str) -> Dict[str, Any]:
        """Parse packet capture content."""
        lines = content.strip().split('\n')
        
        # Parse HTTP method and URL
        first_line = lines[0] if lines else ""
        method = ""
        url = ""
        
        if " " in first_line:
            parts = first_line.split()
            if len(parts) >= 2:
                method = parts[0]
                url = parts[1]
        
        # Parse headers
        headers = {}
        body_start = len(lines)
        
        for i, line in enumerate(lines[1:], 1):
            if line.strip() == "":
                body_start = i + 1
                break
            
            if ":" in line:
                key, value = line.split(":", 1)
                headers[key.strip()] = value.strip()
        
        # Parse body
        body = ""
        if body_start < len(lines):
            body_lines = lines[body_start:]
            body = "\n".join(body_lines).strip()
        
        return {
            "method": method,
            "url": url,
            "headers": headers,
            "body": body
        }
    
    def _compare_requests(self, actual: Dict[str, Any], 
                         expected: Dict[str, Any]) -> Dict[str, Any]:
        """Compare actual request with expected packet data."""
        comparison = {
            "method_match": actual.get("method") == expected.get("method"),
            "url_match": self._compare_urls(actual.get("url", ""), expected.get("url", "")),
            "headers_comparison": self._compare_headers(
                actual.get("headers", {}), 
                expected.get("headers", {})
            ),
            "body_comparison": self._compare_bodies(
                actual.get("body", ""), 
                expected.get("body", "")
            )
        }
        
        comparison["overall_match"] = (
            comparison["method_match"] and 
            comparison["url_match"] and 
            comparison["headers_comparison"]["critical_headers_match"]
        )
        
        return comparison
    
    def _compare_responses(self, actual: Dict[str, Any], 
                          expected: Dict[str, Any]) -> Dict[str, Any]:
        """Compare actual response with expected packet data."""
        comparison = {
            "status_comparison": self._compare_status_codes(
                actual.get("status_code"), 
                expected.get("status_code")
            ),
            "headers_comparison": self._compare_headers(
                actual.get("headers", {}), 
                expected.get("headers", {})
            ),
            "body_comparison": self._compare_bodies(
                actual.get("body", ""), 
                expected.get("body", "")
            )
        }
        
        return comparison
    
    def _compare_urls(self, actual: str, expected: str) -> bool:
        """Compare URLs, ignoring minor differences."""
        # Remove protocol and normalize
        actual_clean = actual.replace("https://", "").replace("http://", "")
        expected_clean = expected.replace("https://", "").replace("http://", "")
        
        return actual_clean == expected_clean
    
    def _compare_headers(self, actual: Dict[str, str], 
                        expected: Dict[str, str]) -> Dict[str, Any]:
        """Compare headers, focusing on critical ones."""
        critical_headers = [
            "content-type", "accept", "user-agent", "origin", 
            "referer", "sec-fetch-site", "sec-fetch-mode"
        ]
        
        critical_matches = {}
        for header in critical_headers:
            actual_value = actual.get(header, "").lower()
            expected_value = expected.get(header, "").lower()
            critical_matches[header] = actual_value == expected_value
        
        return {
            "critical_headers_match": all(critical_matches.values()),
            "critical_matches": critical_matches,
            "missing_headers": [h for h in expected.keys() if h not in actual],
            "extra_headers": [h for h in actual.keys() if h not in expected]
        }
    
    def _compare_bodies(self, actual: str, expected: str) -> Dict[str, Any]:
        """Compare request/response bodies."""
        if not actual and not expected:
            return {"match": True, "type": "both_empty"}
        
        # Try to parse as JSON
        try:
            actual_json = json.loads(actual) if actual else {}
            expected_json = json.loads(expected) if expected else {}
            
            return {
                "match": actual_json == expected_json,
                "type": "json",
                "actual_keys": list(actual_json.keys()) if isinstance(actual_json, dict) else [],
                "expected_keys": list(expected_json.keys()) if isinstance(expected_json, dict) else []
            }
        except json.JSONDecodeError:
            # Compare as text
            return {
                "match": actual.strip() == expected.strip(),
                "type": "text",
                "actual_length": len(actual),
                "expected_length": len(expected)
            }
    
    def _compare_status_codes(self, actual: Optional[int], 
                            expected: Optional[int]) -> Dict[str, Any]:
        """Compare HTTP status codes."""
        return {
            "match": actual == expected,
            "actual": actual,
            "expected": expected
        }


class DebugOutputHelper:
    """Helper for debug output and logging."""
    
    @staticmethod
    def format_verification_result(result: Dict[str, Any]) -> str:
        """Format verification result for display."""
        if not result.get("success"):
            return f"❌ Verification failed: {result.get('error')}"
        
        comparison = result.get("comparison", {})
        output = [f"📋 Verification for {result.get('packet_file')}:"]
        
        if "method_match" in comparison:
            # Request comparison
            output.append(f"  Method: {'✅' if comparison['method_match'] else '❌'}")
            output.append(f"  URL: {'✅' if comparison['url_match'] else '❌'}")
            
            headers_comp = comparison.get("headers_comparison", {})
            output.append(f"  Critical Headers: {'✅' if headers_comp.get('critical_headers_match') else '❌'}")
            
            if not headers_comp.get('critical_headers_match'):
                for header, match in headers_comp.get('critical_matches', {}).items():
                    output.append(f"    {header}: {'✅' if match else '❌'}")
        
        if "status_comparison" in comparison:
            # Response comparison
            status_comp = comparison.get("status_comparison", {})
            output.append(f"  Status Code: {'✅' if status_comp.get('match') else '❌'}")
            if not status_comp.get('match'):
                output.append(f"    Expected: {status_comp.get('expected')}, Got: {status_comp.get('actual')}")
        
        overall = comparison.get("overall_match")
        if overall is not None:
            output.append(f"  Overall: {'✅ PASS' if overall else '❌ FAIL'}")
        
        return "\n".join(output)
    
    @staticmethod
    def log_checkpoint_data(step_name: str, data: Dict[str, Any]) -> None:
        """Log checkpoint data in a formatted way."""
        logger.info(f"=== CHECKPOINT: {step_name} ===")
        
        # Log important fields
        important_fields = ["success", "status_code", "account_id", "api_token", "site_url"]
        for field in important_fields:
            if field in data:
                value = data[field]
                if isinstance(value, str) and len(value) > 50:
                    value = value[:47] + "..."
                logger.info(f"  {field}: {value}")
        
        # Log cookies if present
        if "cookies" in data:
            cookies = data["cookies"]
            logger.info(f"  cookies: {len(cookies)} cookies extracted")
            for name, value in list(cookies.items())[:3]:  # Show first 3 cookies
                logger.info(f"    {name}: {value[:20]}...")
        
        logger.info("=" * (len(step_name) + 20))
