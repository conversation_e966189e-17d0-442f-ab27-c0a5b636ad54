# Atlassian Register

Automated Atlassian account registration and site creation tool based on network packet analysis.

## Features

- 🚀 Automated Atlassian account registration
- 🏗️ DevAI site creation with full configuration
- 🔐 API token generation
- 🤖 CAPTCHA solving integration (YesCaptcha)
- 🛡️ Anti-fraud detection bypass
- 📊 Manual verification checkpoints
- 🔄 Rate limiting and retry logic
- 📝 Comprehensive logging and debugging

## Installation

### Prerequisites

- Python 3.13+
- uv package manager

### Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd atlassian_register
```

2. Create virtual environment and install dependencies:
```bash
uv venv
uv add requests python-dotenv urllib3
```

3. Copy and configure environment variables:
```bash
cp .env.template .env
# Edit .env with your configuration
```

## Configuration

### Required Environment Variables

- `ATLASSIAN_EMAIL`: Email address for registration
- `ATLASSIAN_PASSWORD`: Password for the account
- `YESCAPTCHA_API_KEY`: YesCaptcha service API key

### Optional Configuration

See `.env.template` for all available configuration options.

## Usage

### Command Line Interface

```bash
# Basic usage
atlassian-register --email <EMAIL> --password mypass123

# Using configuration file
atlassian-register --config .env --verbose

# Dry run mode
atlassian-register --email <EMAIL> --dry-run

# With manual verification checkpoints
atlassian-register --config .env --manual-verification --verbose
```

### Python API

```python
from atlassian_register import RegistrationConfig, RegistrationFlow

# Create configuration
config = RegistrationConfig(
    email="<EMAIL>",
    password="mypass123",
    yescaptcha_api_key="your-api-key"
)

# Run registration
flow = RegistrationFlow(config)
result = flow.run_registration()

if result.success:
    print(f"Account ID: {result.account_id}")
    print(f"API Token: {result.api_token}")
    print(f"Site URL: {result.site_url}")
else:
    print(f"Error: {result.error_message}")
```

## Architecture

The tool is organized into several key modules:

- **Config**: Configuration management and validation
- **Client**: HTTP client with session and cookie management
- **CAPTCHA**: YesCaptcha integration and solving
- **Castle**: Browser fingerprinting and token generation
- **Flow**: Main registration flow orchestration
- **CLI**: Command-line interface

## Security Considerations

- Store sensitive credentials in environment variables
- Use secure passwords and enable 2FA where possible
- Be aware of rate limiting and anti-fraud detection
- Consider using proxies for additional anonymity

## Development

### Running Tests

```bash
uv add --dev pytest pytest-cov
uv run pytest
```

### Code Formatting

```bash
uv add --dev black isort flake8
uv run black .
uv run isort .
uv run flake8 .
```

## Disclaimer

This tool is for educational and legitimate automation purposes only. Users are responsible for complying with Atlassian's Terms of Service and applicable laws.

## License

MIT License - see LICENSE file for details.