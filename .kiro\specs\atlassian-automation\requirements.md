# Requirements Document / 需求文档

## Introduction / 简介

This feature automates the complete Atlassian account registration process, including email verification, API token creation, and DevAI site setup. The system will replicate the manual workflow identified from network packet analysis in the `raw/` directory, enabling programmatic account creation for development and testing purposes. The implementation follows KISS (Keep It Simple, Stupid) principles and leverages MCP tools like context7 for documentation and reference.

本功能自动化完整的Atlassian账户注册流程，包括邮箱验证、API令牌创建和DevAI站点设置。系统将复制从`raw/`目录中网络包分析识别的手动工作流程，实现程序化账户创建用于开发和测试目的。实现遵循KISS（保持简单愚蠢）原则，并利用context7等MCP工具进行文档和参考。

## Requirements / 需求

### Requirement 1 / 需求1

**User Story / 用户故事:** As a developer, I want to automatically register new Atlassian accounts with email verification, so that I can create multiple test accounts without manual intervention.

作为开发者，我希望能够自动注册新的Atlassian账户并完成邮箱验证，以便我可以创建多个测试账户而无需手动干预。

#### Acceptance Criteria / 验收标准

1. WHEN the system receives email credentials and account details THEN it SHALL initiate the signup process at `https://id.atlassian.com/signup`
   当系统接收到邮箱凭据和账户详情时，系统应在`https://id.atlassian.com/signup`启动注册流程

2. WHEN the signup form is accessed THEN the system SHALL extract and store all required cookies (`atlassian.account.xsrf.token`, `ajs_anonymous_id`, `cloud.session.token`)
   当访问注册表单时，系统应提取并存储所有必需的cookies（`atlassian.account.xsrf.token`、`ajs_anonymous_id`、`cloud.session.token`）

3. WHEN submitting registration data THEN the system SHALL send a POST request to `/gateway/api/gasv3/api/v1/batch` with email, password, and displayName using the `signupWithEmail` batch operation
   当提交注册数据时，系统应使用`signupWithEmail`批量操作向`/gateway/api/gasv3/api/v1/batch`发送POST请求，包含email、password和displayName

4. WHEN the registration is successful THEN the system SHALL handle the email verification code input automatically
   当注册成功时，系统应自动处理邮箱验证码输入

5. IF reCAPTCHA is required THEN the system SHALL integrate with YesCaptcha service for automated reCAPTCHA solving
   如果需要reCAPTCHA验证，系统应集成YesCaptcha服务进行自动reCAPTCHA解决

### Requirement 2 / 需求2

**User Story / 用户故事:** As a developer, I want the system to automatically verify email addresses using verification codes, so that the registration process completes without manual email checking.

作为开发者，我希望系统能够使用验证码自动验证邮箱地址，以便注册流程无需手动检查邮箱即可完成。

#### Acceptance Criteria / 验收标准

1. WHEN an email verification code is provided THEN the system SHALL submit it via the `verifyEmailWithCode` batch API call
   当提供邮箱验证码时，系统应通过`verifyEmailWithCode`批量API调用提交验证码

2. WHEN verification is successful THEN the system SHALL extract the `accountId` from the response (format: `712020:dcaf3d79-...`)
   当验证成功时，系统应从响应中提取`accountId`（格式：`712020:dcaf3d79-...`）

3. WHEN verification completes THEN the system SHALL update stored cookies with new `cloud.session.token`
   当验证完成时，系统应使用新的`cloud.session.token`更新存储的cookies

4. WHEN the account is verified THEN the system SHALL maintain the authenticated session state
   当账户验证完成时，系统应维持已认证的会话状态

### Requirement 3 / 需求3

**User Story / 用户故事:** As a developer, I want to automatically generate API tokens for registered accounts, so that I can programmatically access Atlassian services.

作为开发者，我希望能够为注册的账户自动生成API令牌，以便我可以程序化访问Atlassian服务。

#### Acceptance Criteria / 验收标准

1. WHEN an account is successfully registered and verified THEN the system SHALL create an API token
   当账户成功注册并验证后，系统应创建API令牌

2. WHEN creating an API token THEN the system SHALL POST to `/gateway/api/users/{accountId}/manage/api-tokens` with proper authentication headers
   当创建API令牌时，系统应使用适当的认证头向`/gateway/api/users/{accountId}/manage/api-tokens`发送POST请求

3. WHEN submitting the token request THEN the system SHALL include `x-atlassian-token` header with the XSRF token value (same as `atlassian.account.xsrf.token` cookie)
   当提交令牌请求时，系统应包含`x-atlassian-token`头，值为XSRF令牌（与`atlassian.account.xsrf.token` cookie相同）

4. WHEN the API token is created THEN the system SHALL extract and securely store the token value (format: `ATATT3xFfGF0_Ae-4tlj0...3E86D956`)
   当API令牌创建时，系统应提取并安全存储令牌值（格式：`ATATT3xFfGF0_Ae-4tlj0...3E86D956`）

5. WHEN setting token expiry THEN the system SHALL allow configurable expiration dates (e.g., `2026-06-16T00:00:00.000Z`)
   当设置令牌过期时间时，系统应允许可配置的过期日期（例如：`2026-06-16T00:00:00.000Z`）

### Requirement 4 / 需求4

**User Story / 用户故事:** As a developer, I want to automatically create DevAI cloud sites for registered accounts, so that I can set up complete development environments programmatically.

作为开发者，我希望能够为注册的账户自动创建DevAI云站点，以便我可以程序化设置完整的开发环境。

#### Acceptance Criteria / 验收标准

1. WHEN an account has a valid API token THEN the system SHALL request a recommended site name from `https://www.atlassian.com/gateway/api/bxp/signup/site/recommended-name`
   当账户拥有有效的API令牌时，系统应从`https://www.atlassian.com/gateway/api/bxp/signup/site/recommended-name`请求推荐的站点名称

2. WHEN requesting site name THEN the system SHALL send email in the request body (e.g., `{"email":"<EMAIL>"}`)
   当请求站点名称时，系统应在请求体中发送邮箱（例如：`{"email":"<EMAIL>"}`）

3. WHEN a site name is recommended THEN the system SHALL receive a response like `{"recommendedName":"wendavid-team-y60a2ps6"}`
   当站点名称被推荐时，系统应接收类似`{"recommendedName":"wendavid-team-y60a2ps6"}`的响应

4. WHEN creating the site THEN the system SHALL POST to `/gateway/api/bxp/signup/site/create` with siteName and DevAI bundle specification
   当创建站点时，系统应向`/gateway/api/bxp/signup/site/create`发送POST请求，包含siteName和DevAI套件规范

5. WHEN the site is created THEN the system SHALL extract and return the `siteUrl` and `orgId`
   当站点创建时，系统应提取并返回`siteUrl`和`orgId`

6. WHEN site creation fails THEN the system SHALL provide clear error messages and retry logic
   当站点创建失败时，系统应提供清晰的错误消息和重试逻辑

### Requirement 5 / 需求5

**User Story / 用户故事:** As a developer, I want proper session and cookie management throughout the automation process, so that all API calls are properly authenticated.

作为开发者，我希望在整个自动化过程中有适当的会话和cookie管理，以便所有API调用都能正确认证。

#### Acceptance Criteria / 验收标准

1. WHEN making any authenticated request THEN the system SHALL include all required cookies (`atlassian.account.xsrf.token`, `cloud.session.token`, `atlassian.account.ffs.id`, `ajs_anonymous_id`, `atlCohort`) in the Cookie header
   当进行任何认证请求时，系统应在Cookie头中包含所有必需的cookies（`atlassian.account.xsrf.token`、`cloud.session.token`、`atlassian.account.ffs.id`、`ajs_anonymous_id`、`atlCohort`）

2. WHEN cookies are updated by the server THEN the system SHALL automatically update the stored cookie values
   当服务器更新cookies时，系统应自动更新存储的cookie值

3. WHEN making POST requests THEN the system SHALL include the `x-atlassian-token` header with the current XSRF token (URI-decoded value from `atlassian.account.xsrf.token` cookie)
   当进行POST请求时，系统应包含`x-atlassian-token`头，值为当前XSRF令牌（从`atlassian.account.xsrf.token` cookie中URI解码的值）

4. WHEN the XSRF token changes THEN the system SHALL update both the cookie and header values
   当XSRF令牌改变时，系统应更新cookie和头部值

5. WHEN session expires THEN the system SHALL detect the failure and provide appropriate error handling
   当会话过期时，系统应检测失败并提供适当的错误处理

### Requirement 6 / 需求6

**User Story / 用户故事:** As a developer, I want configurable rate limiting and error handling, so that the automation respects Atlassian's service limits and handles failures gracefully.

作为开发者，我希望有可配置的速率限制和错误处理，以便自动化工具尊重Atlassian的服务限制并优雅地处理失败。

#### Acceptance Criteria / 验收标准

1. WHEN making multiple requests THEN the system SHALL implement configurable delays between requests (recommended random delays to avoid detection)
   当进行多个请求时，系统应实现请求间的可配置延迟（建议随机延迟以避免检测）

2. WHEN rate limits are encountered THEN the system SHALL implement exponential backoff retry logic
   当遇到速率限制时，系统应实现指数退避重试逻辑

3. WHEN network errors occur THEN the system SHALL retry failed requests up to a configurable maximum
   当发生网络错误时，系统应重试失败的请求，直到可配置的最大次数

4. WHEN permanent failures occur THEN the system SHALL log detailed error information and stop processing
   当发生永久性失败时，系统应记录详细的错误信息并停止处理

5. WHEN debugging is enabled THEN the system SHALL log all HTTP requests and responses for troubleshooting
   当启用调试时，系统应记录所有HTTP请求和响应以便故障排除

6. WHEN same IP/email is used multiple times THEN the system SHALL handle Atlassian's rate limiting for repeated registrations
   当同一IP/邮箱被多次使用时，系统应处理Atlassian对重复注册的速率限制

### Requirement 7 / 需求7

**User Story / 用户故事:** As a developer, I want a simple Python project structure following KISS principles with proper dependency management, so that the automation tool is maintainable and easy to deploy.

作为开发者，我希望有一个遵循KISS原则的简单Python项目结构和适当的依赖管理，以便自动化工具易于维护和部署。

#### Acceptance Criteria / 验收标准

1. WHEN setting up the project THEN the system SHALL use `uv` for Python dependency management
   当设置项目时，系统应使用`uv`进行Python依赖管理

2. WHEN organizing code THEN the system SHALL follow KISS principles with minimal, focused modules (one main module per major function)
   当组织代码时，系统应遵循KISS原则，使用最少的、专注的模块（每个主要功能一个主模块）

3. WHEN handling configuration THEN the system SHALL support simple environment variables for email credentials, YesCaptcha API key, and other parameters
   当处理配置时，系统应支持简单的环境变量，用于邮箱凭据、YesCaptcha API密钥和其他参数

4. WHEN logging events THEN the system SHALL use simple print statements or basic logging for debugging
   当记录事件时，系统应使用简单的print语句或基本日志记录进行调试

5. WHEN packaging the tool THEN the system SHALL provide a simple command-line interface for easy execution
   当打包工具时，系统应提供简单的命令行界面以便于执行

6. WHEN handling sensitive data THEN the system SHALL use basic secure storage for API tokens and credentials
   当处理敏感数据时，系统应使用基本的安全存储来保存API令牌和凭据

7. WHEN implementing features THEN the system SHALL NOT include automated testing but SHALL include manual verification steps after each implementation phase
   当实现功能时，系统不应包含自动化测试，但应在每个实现阶段后包含手动验证步骤

### Requirement 8 / 需求8

**User Story / 用户故事:** As a developer, I want automated reCAPTCHA solving using YesCaptcha service, so that the registration process can bypass CAPTCHA challenges without manual intervention.

作为开发者，我希望使用YesCaptcha服务进行自动reCAPTCHA解决，以便注册流程可以绕过验证码挑战而无需手动干预。

#### Acceptance Criteria / 验收标准

1. WHEN reCAPTCHA is encountered during registration THEN the system SHALL extract the site key and page URL
   当注册过程中遇到reCAPTCHA时，系统应提取站点密钥和页面URL

2. WHEN submitting CAPTCHA to YesCaptcha THEN the system SHALL use the YesCaptcha API with proper credentials and task parameters
   当向YesCaptcha提交验证码时，系统应使用YesCaptcha API和适当的凭据及任务参数

3. WHEN waiting for CAPTCHA solution THEN the system SHALL poll YesCaptcha API until the solution is ready
   当等待验证码解决方案时，系统应轮询YesCaptcha API直到解决方案准备就绪

4. WHEN CAPTCHA solution is received THEN the system SHALL include the g-recaptcha-response token in the registration request
   当收到验证码解决方案时，系统应在注册请求中包含g-recaptcha-response令牌

5. WHEN CAPTCHA solving fails THEN the system SHALL retry with exponential backoff or fail gracefully with clear error messages
   当验证码解决失败时，系统应使用指数退避重试或优雅失败并提供清晰的错误消息

6. WHEN configuring YesCaptcha THEN the system SHALL support API key configuration through environment variables or config files
   当配置YesCaptcha时，系统应支持通过环境变量或配置文件进行API密钥配置

### Requirement 9 / 需求9

**User Story / 用户故事:** As a developer, I want to use MCP tools like context7 for documentation and reference during development, so that I can access up-to-date library documentation and examples.

作为开发者，我希望在开发过程中使用context7等MCP工具进行文档和参考，以便我可以访问最新的库文档和示例。

#### Acceptance Criteria / 验收标准

1. WHEN developing HTTP client functionality THEN the system SHALL use context7 to access requests library documentation
   当开发HTTP客户端功能时，系统应使用context7访问requests库文档

2. WHEN implementing JSON handling THEN the system SHALL reference standard library documentation through MCP tools
   当实现JSON处理时，系统应通过MCP工具参考标准库文档

3. WHEN working with environment variables THEN the system SHALL use MCP tools to access os and dotenv library documentation
   当处理环境变量时，系统应使用MCP工具访问os和dotenv库文档

4. WHEN implementing cookie management THEN the system SHALL reference relevant library documentation through context7
   当实现cookie管理时，系统应通过context7参考相关库文档

### Requirement 10 / 需求10

**User Story / 用户故事:** As a developer, I want to use the captured network packets in the `raw/` directory as the definitive reference for API calls, so that the implementation accurately replicates the observed behavior.

作为开发者，我希望使用`raw/`目录中捕获的网络包作为API调用的权威参考，以便实现准确复制观察到的行为。

#### Acceptance Criteria / 验收标准

1. WHEN implementing API calls THEN the system SHALL reference the exact HTTP headers from files like `147_c.txt`, `225_c.txt`, etc.
   当实现API调用时，系统应参考`147_c.txt`、`225_c.txt`等文件中的确切HTTP头

2. WHEN constructing request payloads THEN the system SHALL use the exact JSON structures found in the captured packets
   当构造请求负载时，系统应使用捕获包中找到的确切JSON结构

3. WHEN handling responses THEN the system SHALL expect the response formats documented in files like `147_s.txt`, `225_s.txt`, etc.
   当处理响应时，系统应期望`147_s.txt`、`225_s.txt`等文件中记录的响应格式

4. WHEN extracting cookies THEN the system SHALL follow the exact cookie handling patterns observed in the packet captures
   当提取cookies时，系统应遵循包捕获中观察到的确切cookie处理模式

5. WHEN implementing each step THEN the system SHALL include a manual verification step to compare output with the reference packets
   当实现每个步骤时，系统应包含手动验证步骤，将输出与参考包进行比较